<ProjectData xmlns="http://schemas.datacontract.org/2004/07/BG8.Supercharger.Features.CodeMap" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ProjectAllTimeMostUsedData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\LoggingHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\LoggingHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\IConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\IConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\PathPreviewForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\PathPreviewForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\ConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\ConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\StationInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\StationInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserMenuOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserMenuOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserTabManager.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserTabManager#标签页管理方法#GetCurrentTabData</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserTabManager#WebView2事件和方法#WebView_NavigationCompleted</CodeMapItemPath><ParameterList>object, CoreWebView2NavigationCompletedEventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserTabManager#WebView2事件和方法#GetSectionIdByTabName</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserTabManager#UpdateCookiePathFileAsync</CodeMapItemPath><ParameterList>WebView2, WebBrowserTabConfig, Dictionary&lt;string, string&gt;, string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerDownload.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>frmChinaTowerdownload#SetButtonText</CodeMapItemPath><ParameterList>Button, string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>frmChinaTowerdownload#全局功能函数#GetHtml</CodeMapItemPath><ParameterList>string, string, string, string</ParameterList><UsageCount>5</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookiePathManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfigForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\TabConfigForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCookieOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCookieOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>HyAssistantLicenseManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyAssistantLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\CookieManagerForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Main.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>MainForm#界面初始化#文件复制助手ToolStripMenuItem_Click</CodeMapItemPath><ParameterList>object, EventArgs</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>Main.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Program.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserResourceManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserResourceManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\IPhotoRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\IPhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerdownload.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>frmChinaTowerdownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\MenuGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\MenuGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserUIHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserUIHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\FormClosingHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\FormClosingHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\CookieManagerForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>VisioPDF\VisioPDF.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>VisioPDF\VisioPDF.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserStatusBridge.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserStatusBridge.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>HaUIPermissionManager.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>HaUIPermissionManager#权限刷新方法#ForceRefreshPermissionsAndUI</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HaUIPermissionManager#权限刷新方法#RefreshMenuPermissionsAsync</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HaUIPermissionManager#权限刷新方法#RefreshMenuPermissionsInternalAsync</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>HaUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionKeeper.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserSessionKeeper#私有方法#DisposeHttpClient</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserSessionKeeper#私有方法#RefreshTimer_Callback</CodeMapItemPath><ParameterList>object</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserSessionKeeper#私有方法#DetermineTargetUrl</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserSessionKeeper#私有方法#KeepSessionAliveAsync</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserSessionKeeper#私有方法#ExecuteHttpRequestAsync</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserSessionKeeper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConstants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>FileCopier\FileCopier.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>FileCopier\FileCopier.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\frmChinaTowerDownload.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>frmChinaTowerDownload#事件处理#frmChinaTowerDownload_FormClosed</CodeMapItemPath><ParameterList>object, FormClosedEventArgs</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>frmChinaTowerDownload#私有方法#TestChinaTowerServerAsync</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\StationRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserV2Constants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserV2Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowser.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowser#事件处理方法#CloseTabMenuItem_Click</CodeMapItemPath><ParameterList>object, EventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowser#事件处理方法#UpdateMainMenuStatus</CodeMapItemPath><ParameterList>bool</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowser#事件处理方法#StatusStrip1_MouseEnter</CodeMapItemPath><ParameterList>object, EventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserCookieManager.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserCookieManager#WebView2事件处理#WebView_WebResourceRequested</CodeMapItemPath><ParameterList>object, CoreWebView2WebResourceRequestedEventArgs</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserCookieManager#Cookie管理方法#GetCookiesFromWebView2Async</CodeMapItemPath><ParameterList>WebView2</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserCookieManager#Cookie管理方法#SetCookiesToWebView2Async</CodeMapItemPath><ParameterList>WebView2, CookieData</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserCookieManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieTransferManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieTransferManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FormClosingHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\FormClosingHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCacheOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCacheOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserExceptionHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserExceptionHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TabMenuManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TabMenuManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserUIOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserUIOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerDownload#窗体事件#ChinaTowerDownload_Load</CodeMapItemPath><ParameterList>object, EventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerDownload#窗体事件#ChinaTowerDownload_FormClosing</CodeMapItemPath><ParameterList>object, FormClosingEventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerHttpService#私有方法#ConfigureSecurityProtocol</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerHttpService#私有方法#ExecuteWithRetryAsync</CodeMapItemPath><ParameterList>Func&lt;Task&lt;T&gt;&gt;, int, TimeSpan</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerHttpService#公共方法#ValidateTokenAsync</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerHttpService#公共方法#GetStationListCountAsync</CodeMapItemPath><ParameterList/><UsageCount>6</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerHttpService#公共方法#GetAllStationsAsync</CodeMapItemPath><ParameterList/><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerHttpService#公共方法#ParsePhotoResponse</CodeMapItemPath><ParameterList>string, string, string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerHttpService#公共方法#DownloadPhotoAsync</CodeMapItemPath><ParameterList>PhotoInfoExcerpt, string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerHttpService#公共方法#BatchDownloadPhotosAsync</CodeMapItemPath><ParameterList>List&lt;PhotoInfoExcerpt&gt;, string, int, IProgress&lt;DownloadProgress&gt;</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerHttpService#公共方法#DownloadSinglePhotoAsync</CodeMapItemPath><ParameterList>PhotoInfoExcerpt, string, SemaphoreSlim, DownloadResult, IProgress&lt;DownloadProgress&gt;</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerHttpService#公共方法#ClearAuthentication</CodeMapItemPath><ParameterList/><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ChinaTowerHttpService#IDisposable#Dispose</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Main.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Main.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserSessionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\PhotoRepository.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>PhotoRepository#UpdateDownloadStatusAsync</CodeMapItemPath><ParameterList>long, long</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\Data\PhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfig.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserTabConfig#CookiePath</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserTabConfig#LastRefreshTime</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserTabConfig#ExtraHeaders</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserTabConfig#StartupDelaySeconds</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>WebBrowserTabConfig#ApiUrls</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>WebBrowser\TabConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\NewFolderManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\NewFolderManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>HaPermissionKeys.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HaPermissionKeys.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>FileAnalyzer\FileAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>FileAnalyzer\FileAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w></ProjectAllTimeMostUsedData><ProjectExpandedStateData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\LoggingHelperV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>LoggingHelperV2#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>LoggingHelperV2#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>LoggingHelperV2#基础日志方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>LoggingHelperV2#结构化日志方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>LoggingHelperV2#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>LoggingHelperV2#IDisposable实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\LoggingHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\IConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\IConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\PathPreviewForm.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>PathPreviewForm#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>PathPreviewForm#公共属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>PathPreviewForm#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>PathPreviewForm#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>PathPreviewForm#私有方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>NewFolder\PathPreviewForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\ConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\ConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\StationInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\StationInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserMenuOperations.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#菜单事件注册和初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#标签页菜单管理器事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#右键菜单项点击事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Core\WebBrowserMenuOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserTabManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManager#字段和属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManager#字段和属性#TabUrlChangedEventHandler</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManager#字段和属性#TabTitleChangedEventHandler</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManager#字段和属性#TabSwitchedEventHandler</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManager#IDisposable实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerDownload.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>frmChinaTowerdownload#登录</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frmChinaTowerdownload#定义变量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frmChinaTowerdownload#按服务器清单下载照片</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathManagerV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>CookiePathManagerV2#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookiePathManagerV2#公共属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookiePathManagerV2#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookiePathManagerV2#路径验证和管理方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookiePathManagerV2#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookiePathManagerV2#IDisposable实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\CookiePathManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfigForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\TabConfigForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCookieOperations.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#Cookie设置和导航</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCookieOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManagerV2#字段和属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManagerV2#字段和属性#TabUrlChangedEventHandler</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManagerV2#字段和属性#TabTitleChangedEventHandler</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManagerV2#字段和属性#TabSwitchedEventHandler</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManagerV2#内部类</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManagerV2#构造方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManagerV2#标签页操作方法（兼容性方法）</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManagerV2#Cookie数据传递方法（V2版本关键优化）</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManagerV2#IDisposable实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManagerV2#V2新增辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabManagerV2#WebView2线程安全操作方法（V2新增）</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>HyAssistantLicenseManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>HyAssistantLicenseManager#常量定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyAssistantLicenseManager#静态字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyAssistantLicenseManager#授权控制器初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyAssistantLicenseManager#权限管理器初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyAssistantLicenseManager#权限检查方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyAssistantLicenseManager#权限刷新方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyAssistantLicenseManager#网络授权更新回调</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyAssistantLicenseManager#授权状态刷新</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HyAssistantLicenseManager#公共属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>HyAssistantLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\CookieManagerForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>OperationRecoveryManagerV2#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OperationRecoveryManagerV2#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>OperationRecoveryManagerV2#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>辅助类和枚举</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Main.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>MainForm#构造函数和初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>MainForm#授权相关</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>MainForm#日志相关</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>MainForm#文本框相关</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>MainForm#通信服务相关</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>MainForm#界面初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>MainForm#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Main.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Program.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserResourceManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserResourceManager#字段和属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserResourceManager#构造方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserResourceManager#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserResourceManager#IDisposable实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserResourceManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>RetryStrategyManagerV2#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>RetryStrategyManagerV2#私有方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>辅助类</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerConfig#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerConfig#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerConfig#HTTP请求配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerConfig#服务器配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerConfig#其他配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerConfig#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\IPhotoRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\IPhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>CookieManagerFormV2#构造方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookieManagerFormV2#事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookieManagerFormV2#数据操作方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookieManagerFormV2#按钮事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#窗体事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#工具栏事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#全局异常处理事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#Cookie和缓存操作事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#地址栏和导航事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#标签页事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#状态栏和会话日志事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>BoundaryAndExceptionTesterV2#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>数据类定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerdownload.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>frmChinaTowerdownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\MenuGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\MenuGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserUIHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserUIHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\FormClosingHandler.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>FormClosingHandler#私有方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowser\FormClosingHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>CookieManagerForm#字段和属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookieManagerForm#构造方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowser\CookieManagerForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserConfigManagerV2#构造和初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserConfigManagerV2#配置操作方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>IDisposable实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>VisioPDF\VisioPDF.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>VisioPDF#转PDF相关</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>VisioPDF#定时执行转PDF</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>VisioPDF\VisioPDF.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>AsyncExceptionHandlerV2#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>AsyncExceptionHandlerV2#静态构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>辅助类</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserStatusBridge.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserStatusBridge.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>HaUIPermissionManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>HaUIPermissionManager#字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>HaUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#核心字段和属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#公共事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#兼容性属性（用于MenuOperations等partial class）</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Core\WebBrowserV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionKeeper.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserSessionKeeper#字段和属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserSessionKeeper#字段和属性#SessionLogUpdateEventHandler</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserSessionKeeper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConstants.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserConstants#时间间隔常量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserConstants#UI常量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserConstants#会话状态常量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>FileCopier\FileCopier.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>FileCopier\FileCopier.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\frmChinaTowerDownload.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>frmChinaTowerDownload#字段和属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>frmChinaTowerDownload#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\StationRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserCookieManagerV2#字段和属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserCookieManagerV2#构造方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserCookieManagerV2#CookiePath生成优化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserCookieManagerV2#标头捕获方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserCookieManagerV2#文件操作方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserCookieManagerV2#自动导出功能</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserCookieManagerV2#工具方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserCookieManagerV2#IDisposable实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserV2Constants.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#超时配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#缓存配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#性能监控配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#网络和URL配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#Cookie配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#UI配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#批处理配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#日志配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#错误处理配置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#版本信息</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#功能开关</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2Constants#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserV2Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>CookieManagerFormTransferTesterV2#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>数据类定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowser.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowser#字段和属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowser#界面</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowser#构造和初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowser#事件处理方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowser#上下文菜单</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowser#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>TabConfigFormV2#字段和属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserCookieManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserCookieManager#IDisposable实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserCookieManager#标头操作方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserCookieManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieTransferManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieTransferManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FormClosingHandlerV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>FormClosingHandlerV2#事件注册</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>FormClosingHandlerV2#事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>FormClosingHandlerV2#关闭检查和清理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>FormClosingHandlerV2#IDisposable实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\FormClosingHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCacheOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCacheOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserExceptionHandlerV2#全局异常处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>CookieFunctionalityTesterV2#CookiePath生成测试</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookieFunctionalityTesterV2#Cookie保存和加载测试</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookieFunctionalityTesterV2#Cookie数据传递测试</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookieFunctionalityTesterV2#Cookie管理窗体测试</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookieFunctionalityTesterV2#测试报告生成</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>CookieFunctionalityTesterV2#资源释放</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>数据结构定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserExceptionHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserExceptionHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConfigManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserConfigManager#构造和初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TabMenuManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TabMenuManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserUIOperations.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#会话日志UI操作</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#鼠标监控和UI交互</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#状态栏和工具栏UI操作</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#窗体显示和隐藏操作</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserV2#对话框和消息框操作</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Core\WebBrowserUIOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>UserFriendlyErrorManagerV2#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>UserFriendlyErrorManagerV2#静态构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>UserFriendlyErrorManagerV2#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>辅助类和枚举</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerDownload#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerDownload#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerDownload#初始化方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerDownload#窗体事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerHttpService#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerHttpService#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerHttpService#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ChinaTowerHttpService#IDisposable</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserUIHelperV2#导航按钮状态更新</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserUIHelperV2#控件文本安全更新</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserUIHelperV2#窗体标题更新</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>WebBrowserUIHelperV2#批量控件更新</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Main.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Main.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserSessionManager#字段和属性#SessionLogUpdateEventHandler</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserSessionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\PhotoRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\PhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfig.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>WebBrowserTabConfig#配置验证方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>WebBrowser\TabConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\NewFolderManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>NewFolderManager#单例模式</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>NewFolderManager#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>NewFolderManager#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>NewFolderManager#私有方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>NewFolderManager#IDisposable实现</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>NewFolder\NewFolderManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>HaPermissionKeys.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>HaPermissionKeys#权限描述映射</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HaPermissionKeys#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>HaPermissionKeys.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>FileAnalyzer\FileAnalyzer.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>FileAnalyzer#常量和字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>FileAnalyzer#实时文件监控</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>FileAnalyzer#工具方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>FileAnalyzer\FileAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w></ProjectExpandedStateData><ProjectFavoriteData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\LoggingHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\LoggingHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\IConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\IConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\PathPreviewForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\PathPreviewForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\ConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\ConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\StationInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\StationInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserMenuOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserMenuOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserTabManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerDownload.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookiePathManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfigForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\TabConfigForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCookieOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCookieOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>HyAssistantLicenseManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyAssistantLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\CookieManagerForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Main.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Main.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Program.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserResourceManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserResourceManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\IPhotoRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\IPhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerdownload.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>frmChinaTowerdownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\MenuGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\MenuGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserUIHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserUIHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\FormClosingHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\FormClosingHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\CookieManagerForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>VisioPDF\VisioPDF.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>VisioPDF\VisioPDF.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserStatusBridge.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserStatusBridge.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>HaUIPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HaUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionKeeper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserSessionKeeper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConstants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>FileCopier\FileCopier.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>FileCopier\FileCopier.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\frmChinaTowerDownload.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\StationRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserV2Constants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserV2Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowser.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserCookieManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserCookieManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieTransferManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieTransferManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FormClosingHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\FormClosingHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCacheOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCacheOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserExceptionHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserExceptionHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TabMenuManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TabMenuManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserUIOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserUIOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Main.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Main.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserSessionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\PhotoRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\PhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\TabConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\NewFolderManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\NewFolderManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>HaPermissionKeys.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HaPermissionKeys.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>FileAnalyzer\FileAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>FileAnalyzer\FileAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w></ProjectFavoriteData><ProjectHistoryData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\LoggingHelperV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>312</Char><CodeMapItemPath>LoggingHelperV2#异常日志方法#LogAsyncException</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T21:09:18.1565075+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Exception, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\LoggingHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\IConfigReader.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>IConfigReader#CreateDefaultConfig</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-13T23:03:40.0674927+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>NewFolder\IConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\PathPreviewForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>73</Char><CodeMapItemPath>PathPreviewForm#事件处理#BtnCreateAndOpen_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T11:13:07.8897391+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>NewFolder\PathPreviewForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\ConfigReader.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>202</Char><CodeMapItemPath>ConfigReader#ConfigFileExists</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-13T23:07:46.6305582+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>310</Char><CodeMapItemPath>ConfigReader#ValidateConfigFile</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-13T21:22:08.2265139+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>-12</Char><CodeMapItemPath>ConfigReader#CreateDefaultConfig</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-13T23:15:12.0798042+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>NewFolder\ConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\StationInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\StationInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserMenuOperations.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>812</Char><CodeMapItemPath>WebBrowserV2#更多右键菜单项点击事件#ViewSessionStatusMenuItem_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-15T22:40:22.4390958+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>983</Char><CodeMapItemPath>WebBrowserV2#更多右键菜单项点击事件#CloneTabMenuItem_Click</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-15T21:25:53.9278193+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>211</Char><CodeMapItemPath>WebBrowserV2#更多右键菜单项点击事件#ManageCookiesMenuItem_Click</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-15T21:01:35.1675056+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>201</Char><CodeMapItemPath>WebBrowserV2#更多右键菜单项点击事件#GetSessionStatusInfo</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T23:00:19.1212487+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object</ParameterList></HistoryDataItem><HistoryDataItem><Char>19</Char><CodeMapItemPath>WebBrowserV2#右键菜单管理#ShowTabContextMenu</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-15T21:01:48.5130609+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Point</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Core\WebBrowserMenuOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserTabManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserTabManager#标签页管理方法#GetCurrentTabData</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-14T23:51:59.8792597+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserTabManager#WebView2事件和方法#GetFormattedUrl</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T00:00:01.197222+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserTabManager#WebView2事件和方法#WebView_NavigationCompleted</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-14T21:24:52.5342528+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, CoreWebView2NavigationCompletedEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>5316</Char><CodeMapItemPath>WebBrowserTabManager#WebView2事件和方法#WebView_WebResourceRequested</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-14T23:35:09.5574304+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, CoreWebView2WebResourceRequestedEventArgs, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserTabManager#WebView2事件和方法#GetSectionIdByTabName</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-14T23:51:59.7388713+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>4098</Char><CodeMapItemPath>WebBrowserTabManager#UpdateCookiePathFileAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-14T23:59:44.8047307+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebView2, WebBrowserTabConfig, Dictionary&lt;string, string&gt;, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1154</Char><CodeMapItemPath>CookieManagerFormTransferTestExecutorV2#ExecuteCompleteTestAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T20:39:05.1831802+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerDownload.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1019</Char><CodeMapItemPath>frmChinaTowerdownload#登录#button登录_Click</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-03T22:30:23.4378435+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>frmChinaTowerdownload#SetButtonText</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-03T23:37:17.0364792+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Button, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>297</Char><CodeMapItemPath>frmChinaTowerdownload#button输入Key_Click</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-03T23:45:48.891602+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>1967</Char><CodeMapItemPath>frmChinaTowerdownload#初始化#ReadIniConfig</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-03T23:43:04.4489432+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>1412</Char><CodeMapItemPath>frmChinaTowerdownload#全局功能函数#GetHtml</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-04T15:24:50.639815+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>118</Char><CodeMapItemPath>frmChinaTowerdownload#界面控制#fileSelectControl存放路径_Selected</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-03T23:43:19.0954788+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>524</Char><CodeMapItemPath>frmChinaTowerdownload#搜索站点，更新站点信息到数据库#GetStationListCount</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-04T15:22:57.6227489+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathManagerV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>246</Char><CodeMapItemPath>CookiePathManagerV2#核心路径生成方法#GenerateSectionBasedPath</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T22:01:07.1117128+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebBrowserTabConfig</ParameterList></HistoryDataItem><HistoryDataItem><Char>272</Char><CodeMapItemPath>CookiePathManagerV2#核心路径生成方法#ConvertToETCookieItems</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-15T21:04:57.0538041+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;CookieItem&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>-11</Char><CodeMapItemPath>CookiePathManagerV2#路径验证和管理方法#WriteCookieFileWithCurrentUrlAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-15T21:03:05.619962+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebView2, WebBrowserTabConfig, CookieData, Dictionary&lt;string, string&gt;</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\CookiePathManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfigForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1323</Char><CodeMapItemPath>TabConfigForm#SaveConfigFromControls</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-10T12:17:56.1997384+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>3692</Char><CodeMapItemPath>TabConfigForm#CloneConfig</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-14T21:22:15.0310468+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebBrowserTabConfig</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowser\TabConfigForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCookieOperations.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>781</Char><CodeMapItemPath>WebBrowserV2#Cookie配置生成和复制#GenerateAndCopyConfigurationAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T20:06:30.7760573+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebView2, WebBrowserTabConfig</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCookieOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1644</Char><CodeMapItemPath>BoundaryAndExceptionTestExecutorV2#ShowTestResults</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T20:38:17.4694781+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>BoundaryTestSummary</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1021</Char><CodeMapItemPath>WebBrowserTabManagerV2#标签页管理方法#CloseTabAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T20:54:06.8705881+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>1218</Char><CodeMapItemPath>WebBrowserTabManagerV2#标签页管理方法#CleanupTabResourcesAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-15T22:51:19.8257064+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>TabDataV2</ParameterList></HistoryDataItem><HistoryDataItem><Char>410</Char><CodeMapItemPath>WebBrowserTabManagerV2#标签页操作方法（兼容性方法）#LogoutCurrentTab</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-15T23:05:54.3315336+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>932</Char><CodeMapItemPath>WebBrowserTabManagerV2#Cookie数据传递方法（V2版本关键优化）#OpenCookieManagerWithSafeTransferAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-15T23:06:07.0350649+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>HyAssistantLicenseManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>250</Char><CodeMapItemPath>HyAssistantLicenseManager#用户组管理#LoadUserGroupsFromConfig</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-28T15:38:52.0978775+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>HyAssistantLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>6845</Char><CodeMapItemPath>CookieManagerForm#Windows Form Designer generated code#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-10T16:02:24.5380104+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowser\CookieManagerForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>413</Char><CodeMapItemPath>OperationRecoveryManagerV2#私有方法#ShouldRetry</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T21:08:30.6522498+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Exception, int, int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Main.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1226</Char><CodeMapItemPath>MainForm#授权相关#InitializeFeaturesAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-29T15:04:14.7823751+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>1083</Char><CodeMapItemPath>MainForm#日志相关#CheckAndUpdateLicenseInfo</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-06-29T00:15:19.2656713+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>MainForm#界面初始化#文件复制助手ToolStripMenuItem_Click</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-29T11:15:35.1923323+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>393</Char><CodeMapItemPath>MainForm#界面初始化#中国铁塔照片下载助手ToolStripMenuItem_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-03T23:16:03.8265374+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Main.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Program.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>993</Char><CodeMapItemPath>Program#Main</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T10:10:29.1860333+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserResourceManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserResourceManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>350</Char><CodeMapItemPath>RetryStrategyManagerV2#公共方法#CreateWebView2Strategy</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T21:09:16.3866569+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>157</Char><CodeMapItemPath>ChinaTowerConfig#构造函数#ChinaTowerConfig</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-03T21:01:16.936472+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ChinaTowerConfig#API接口配置#PhotoUrl</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-05T21:54:03.325012+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>143</Char><CodeMapItemPath>ChinaTowerConfig#辅助方法#GetDatabaseConnectionString</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-03T20:43:17.2480389+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>31</Char><CodeMapItemPath>ChinaTowerConfig#辅助方法#ClearAuthentication</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-03T21:26:20.6188454+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\IPhotoRepository.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>IPhotoRepository#GetUndownloadedPhotosAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-05T09:30:28.8708852+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>IPhotoRepository#InsertPhotosAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-05T21:19:48.4880296+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;PhotoInfoExcerpt&gt;</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\Data\IPhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1252</Char><CodeMapItemPath>WebBrowserV2#V2版本新增方法占位符#SetCookiesToWebViewAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T17:32:18.2690674+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebView2, ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData</ParameterList></HistoryDataItem><HistoryDataItem><Char>466</Char><CodeMapItemPath>WebBrowserV2#UI状态管理优化#UpdateMenuItemStateSafely</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-16T17:08:14.8971714+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, bool, bool?</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>61</Char><CodeMapItemPath>BoundaryAndExceptionTesterV2#TestResourceExhaustionScenariosAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T20:38:10.0161422+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerdownload.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>23</Char><CodeMapItemPath>frmChinaTowerdownload#Dispose</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-03T23:42:37.6876671+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>7678</Char><CodeMapItemPath>frmChinaTowerdownload#Windows Form Designer generated code#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-03T23:42:52.9857037+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>frmChinaTowerdownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\MenuGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\MenuGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserUIHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserUIHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\FormClosingHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\FormClosingHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>70</Char><CodeMapItemPath>CookieManagerForm#事件处理#BtnGetCookies_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-14T21:57:07.4665919+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>2513</Char><CodeMapItemPath>CookieManagerForm#事件处理#ProcessHeadersDataInternal</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-10T16:02:20.6986238+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Dictionary&lt;string, string&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>555</Char><CodeMapItemPath>CookieManagerForm#事件处理#VerifyCookiesInForm</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-10T12:16:56.173858+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, int</ParameterList></HistoryDataItem><HistoryDataItem><Char>70</Char><CodeMapItemPath>CookieManagerForm#粘贴板内容到Cookies_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T00:04:57.393426+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>1833</Char><CodeMapItemPath>CookieManagerForm#Cookie文件处理#HandlePasteOperation</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-08T10:11:26.8413821+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowser\CookieManagerForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>329</Char><CodeMapItemPath>WebBrowserConfigManagerV2#构造和初始化#WebBrowserConfigManagerV2</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T21:05:35.4051522+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object</ParameterList></HistoryDataItem><HistoryDataItem><Char>697</Char><CodeMapItemPath>WebBrowserConfigManagerV2#构造和初始化#CreateExampleConfigAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-15T22:16:13.0781388+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>412</Char><CodeMapItemPath>WebBrowserConfigManagerV2#IDisposable实现#Dispose</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-15T21:09:06.5633974+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>VisioPDF\VisioPDF.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>57</Char><CodeMapItemPath>VisioPDF#初始化,界面#BindControlsToIniSettings</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-28T18:29:54.1317991+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>VisioPDF\VisioPDF.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>2959</Char><CodeMapItemPath>AsyncExceptionHandlerV2#公共方法#ExecuteSafelyAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-15T22:54:35.6652926+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Func&lt;Task&lt;T&gt;&gt;, string, object, TimeSpan?, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>322</Char><CodeMapItemPath>AsyncExceptionHandlerV2#公共方法#GetActiveOperationStats</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-15T21:03:02.368704+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>60</Char><CodeMapItemPath>AsyncExceptionHandlerV2#私有方法#HandleWebView2Exception</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T22:54:50.5269292+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, CoreWebView2Exception, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserStatusBridge.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserStatusBridge.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>HaUIPermissionManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>205</Char><CodeMapItemPath>HaUIPermissionManager#初始化方法#Initialize</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-28T15:32:31.3159498+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>200</Char><CodeMapItemPath>HaUIPermissionManager#权限刷新方法#ForceRefreshPermissionsAndUI</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-06-28T15:29:42.5311103+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>HaUIPermissionManager#权限刷新方法#RefreshMenuPermissionsAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-28T15:29:43.0002224+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>1410</Char><CodeMapItemPath>HaUIPermissionManager#权限刷新方法#RefreshMenuPermissionsInternalAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T10:04:30.2185381+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>HaUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserV2#资源释放#Dispose</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T23:08:39.9423119+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Core\WebBrowserV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionKeeper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserSessionKeeper#私有方法#DisposeHttpClient</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-10T16:43:27.1477252+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserSessionKeeper#私有方法#RefreshTimer_Callback</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-10T16:43:29.4994274+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object</ParameterList></HistoryDataItem><HistoryDataItem><Char>1472</Char><CodeMapItemPath>WebBrowserSessionKeeper#私有方法#DetermineTargetUrl</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-14T21:23:11.2373412+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>2012</Char><CodeMapItemPath>WebBrowserSessionKeeper#私有方法#KeepSessionAliveAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-10T22:20:04.0342044+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>3276</Char><CodeMapItemPath>WebBrowserSessionKeeper#私有方法#ExecuteHttpRequestAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-10T16:56:11.1668911+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserSessionKeeper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConstants.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserConstants#文件夹常量#CONFIG_FILE</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T20:52:37.8319932+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>FileCopier\FileCopier.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>FileCopier#FileCopier</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-29T11:15:56.8778406+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>147</Char><CodeMapItemPath>FileCopier#checkBoxFileCopier_CheckStateChanged</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T15:20:27.4239532+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>FileCopier\FileCopier.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\frmChinaTowerDownload.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>64</Char><CodeMapItemPath>frmChinaTowerDownload#事件处理#btnTest_Click</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-04T18:04:07.4650303+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>frmChinaTowerDownload#事件处理#frmChinaTowerDownload_FormClosed</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-04T19:36:16.4753558+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, FormClosedEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>frmChinaTowerDownload#私有方法#TestChinaTowerServerAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-04T19:36:18.3574319+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\StationRepository.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>670</Char><CodeMapItemPath>StationRepository#GetStationByCodeAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-03T16:51:21.2268718+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>784</Char><CodeMapItemPath>StationRepository#GetStationByIdAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-03T20:15:51.018762+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>96</Char><CodeMapItemPath>StationRepository#StationExistsInternalAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-03T18:01:30.6346721+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>SQLiteConnection, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1305</Char><CodeMapItemPath>WebBrowserCookieManagerV2#Cookie操作方法#SetCookiesInternalAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T22:54:11.3460291+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebView2, CookieData</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserV2Constants.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserV2Constants#文件和路径配置#CONFIG_FILE_NAME</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T21:19:18.6027877+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserV2Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>45</Char><CodeMapItemPath>CookieManagerFormTransferTesterV2#RunCompleteTestAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T20:38:50.1507726+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowser.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>607</Char><CodeMapItemPath>WebBrowser#构造和初始化#WebBrowser</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-10T10:12:06.7049951+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowser#事件处理方法#CloseTabMenuItem_Click</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-08T14:43:17.3888786+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowser#事件处理方法#UpdateMainMenuStatus</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-08T14:43:48.4255538+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>846</Char><CodeMapItemPath>WebBrowser#事件处理方法#StatusStrip1_MouseEnter</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-10T09:21:33.3004988+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>6016</Char><CodeMapItemPath>WebBrowser#事件处理方法#EditTabSettingsMenuItem_Click</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-10T09:45:43.3358206+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>1143</Char><CodeMapItemPath>WebBrowser#事件处理方法#ManageCookiesMenuItem_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-10T14:45:15.3563168+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>80</Char><CodeMapItemPath>WebBrowser#CopyCookiesConfigButton_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-14T21:58:46.2905874+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>543</Char><CodeMapItemPath>TabConfigFormV2#配置操作方法#LoadConfigToControls</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-15T21:08:54.7502483+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>1213</Char><CodeMapItemPath>TabConfigFormV2#配置操作方法#ValidateConfigAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T22:01:07.1242382+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserCookieManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserCookieManager#WebView2事件处理#WebView_WebResourceRequested</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-14T21:39:16.0844301+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, CoreWebView2WebResourceRequestedEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>5416</Char><CodeMapItemPath>WebBrowserCookieManager#Cookie管理方法#GetCookiesFromWebView2InternalAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-14T23:45:43.8497984+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebView2</ParameterList></HistoryDataItem><HistoryDataItem><Char>1311</Char><CodeMapItemPath>WebBrowserCookieManager#Cookie管理方法#SetCookiesToWebView2Async</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-10T14:45:15.5674391+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebView2, CookieData</ParameterList></HistoryDataItem><HistoryDataItem><Char>18</Char><CodeMapItemPath>WebBrowserCookieManager#Cookie管理方法#SetCookiesInternal</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-10T12:42:26.0964839+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebView2, CookieData</ParameterList></HistoryDataItem><HistoryDataItem><Char>1152</Char><CodeMapItemPath>WebBrowserCookieManager#Cookie管理方法#SetupAutoCookieExport</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-09T22:09:06.9660682+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebView2, string, int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserCookieManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieTransferManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieTransferManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FormClosingHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\FormClosingHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCacheOperations.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>457</Char><CodeMapItemPath>WebBrowserV2#缓存清理操作#ExecuteCacheClearingAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T20:06:34.5588378+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCacheOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>43</Char><CodeMapItemPath>WebBrowserExceptionHandlerV2#异常处理主方法#HandleTaskCanceledException</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-15T21:09:06.6249238+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, TaskCanceledException, string, bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>334</Char><CodeMapItemPath>WebBrowserExceptionHandlerV2#异常处理主方法#HandleWebView2Exception</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-15T22:52:06.8711589+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, Exception, string, bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>43</Char><CodeMapItemPath>WebBrowserExceptionHandlerV2#用户友好错误提示#GetWebView2UserFriendlyMessage</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T23:00:09.3572784+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>CoreWebView2Exception</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>617</Char><CodeMapItemPath>CookieFunctionalityTesterV2#Cookie文件格式测试#TestJsonFormatValidation</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T20:38:32.2615199+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>IChinaTowerHttpService#ValidateTokenAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-03T21:08:32.324104+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>IChinaTowerHttpService#TestAuthorizationOnlyPostAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-03T22:03:42.2023508+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserExceptionHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserExceptionHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConfigManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>565</Char><CodeMapItemPath>WebBrowserConfigManager#构造和初始化#CreateExampleConfig</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-10T12:20:42.4070275+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>246</Char><CodeMapItemPath>WebBrowserConfigManager#配置操作方法#ReloadConfig</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-10T21:45:32.5157611+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TabMenuManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TabMenuManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserUIOperations.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>131</Char><CodeMapItemPath>WebBrowserV2#UI状态更新方法#UpdateUIForEmptyTabs</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T22:40:22.4019827+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>263</Char><CodeMapItemPath>WebBrowserV2#UI状态更新方法#UpdateUIForActiveTab</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-15T20:58:51.6607423+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>27</Char><CodeMapItemPath>WebBrowserV2#控件状态批量更新#BatchUpdateControls</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T21:08:45.5673048+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>UIUpdateActionV2[]</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Core\WebBrowserUIOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>2997</Char><CodeMapItemPath>UserFriendlyErrorManagerV2#私有方法#InitializeErrorTemplates</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-15T22:51:49.3131333+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>33</Char><CodeMapItemPath>UserFriendlyErrorManagerV2#私有方法#GetWebView2ErrorInfo</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T22:59:16.7558149+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>CoreWebView2Exception, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>865</Char><CodeMapItemPath>ChinaTowerDownload#事件处理#ButtonLogin_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-07T15:59:43.4421814+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>59</Char><CodeMapItemPath>ChinaTowerDownload#事件处理#ButtonInputAuth_Click</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-05T00:04:34.985714+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>74</Char><CodeMapItemPath>ChinaTowerDownload#事件处理#ButtonCrawlPhotos_Click</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-07T11:49:09.0279995+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>2101</Char><CodeMapItemPath>ChinaTowerDownload#事件处理#ButtonCrawlAllStations_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-07T14:53:27.2707042+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>460</Char><CodeMapItemPath>ChinaTowerDownload#辅助方法#ValidateAuthenticationAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-05T00:06:18.755766+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ChinaTowerHttpService#私有方法#ConfigureSecurityProtocol</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-05T10:17:02.079673+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>829</Char><CodeMapItemPath>ChinaTowerHttpService#私有方法#GetAsync</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-05T18:06:46.7215402+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>1025</Char><CodeMapItemPath>ChinaTowerHttpService#私有方法#PostAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-09T11:58:02.189564+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>566</Char><CodeMapItemPath>ChinaTowerHttpService#公共方法#GetStationListCountAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-05T00:06:26.9418708+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>971</Char><CodeMapItemPath>ChinaTowerHttpService#公共方法#GetStationPhotosAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-05T18:15:08.0263842+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>StationInfoExcerpt</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ChinaTowerHttpService#公共方法#ParsePhotoResponse</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-05T10:17:15.6168798+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>990</Char><CodeMapItemPath>ChinaTowerHttpService#公共方法#CreatePhotoInfo</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-03T20:35:10.1026963+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, dynamic</ParameterList></HistoryDataItem><HistoryDataItem><Char>613</Char><CodeMapItemPath>ChinaTowerHttpService#公共方法#DownloadPhotoAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-05T10:17:17.6004022+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>PhotoInfoExcerpt, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>136</Char><CodeMapItemPath>ChinaTowerHttpService#公共方法#BatchDownloadPhotosAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-05T22:11:29.734377+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;PhotoInfoExcerpt&gt;, string, int, IProgress&lt;DownloadProgress&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>923</Char><CodeMapItemPath>ChinaTowerHttpService#公共方法#DownloadSinglePhotoAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-05T21:54:03.4894268+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>PhotoInfoExcerpt, string, SemaphoreSlim, DownloadResult, IProgress&lt;DownloadProgress&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ChinaTowerHttpService#公共方法#ClearAuthentication</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-05T10:17:33.7691844+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ChinaTowerHttpService#IDisposable#Dispose</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-05T10:17:39.6283163+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>329</Char><CodeMapItemPath>WebBrowserUIHelperV2#状态栏更新#UpdateRefreshStatusLabelsInternal</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-15T23:38:46.6662447+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>StatusStrip, WebBrowserTabConfig</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Main.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>835</Char><CodeMapItemPath>MainForm#Windows 窗体设计器生成的代码#InitializeComponent</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T15:04:09.8950823+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>Main.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>204</Char><CodeMapItemPath>WebBrowserSessionManager#会话管理方法#GetTabNameBySectionId</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-09T22:09:36.5893152+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowser\WebBrowserSessionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\PhotoRepository.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>PhotoRepository#GetUndownloadedPhotosAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-05T09:30:37.0427445+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>199</Char><CodeMapItemPath>PhotoRepository#GetFailedPhotosAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-03T17:18:24.1184166+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>PhotoRepository#InsertPhotosAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-05T09:30:51.3797685+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;PhotoInfoExcerpt&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>PhotoRepository#UpdateDownloadStatusAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-05T09:30:48.5354109+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>long, long</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ChinaTowerDownload\Data\PhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfig.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserTabConfig#Name</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-14T23:25:57.1944903+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserTabConfig#HttpClientRefreshUrlOrKey</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-14T21:35:29.6657998+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserTabConfig#CookiePath</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-14T21:57:49.1945921+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserTabConfig#ExtraHeaders</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-14T21:21:47.8414234+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>WebBrowserTabConfig#ApiUrls</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-14T21:35:00.6709116+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>WebBrowser\TabConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\NewFolderManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>284</Char><CodeMapItemPath>NewFolderManager#公共方法#CreateDefaultConfig</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-13T22:26:45.6784713+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>265</Char><CodeMapItemPath>NewFolderManager#公共方法#EnsureConfigFileExists</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-13T23:36:27.3211393+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>NewFolder\NewFolderManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>HaPermissionKeys.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>11</Char><CodeMapItemPath>HaPermissionKeys#功能权限#WebBrowserv2</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T10:05:25.8666752+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>HaPermissionKeys.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>FileAnalyzer\FileAnalyzer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>119</Char><CodeMapItemPath>FileAnalyzer#初始化,界面#FileAnalyzer</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T15:22:22.3590508+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>170</Char><CodeMapItemPath>FileAnalyzer#初始化,界面#BindControlsToIniSettings</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-28T14:21:14.0397801+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>41</Char><CodeMapItemPath>FileAnalyzer#初始化,界面#notifyIcon1_MouseDoubleClick</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T14:41:02.3408643+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, MouseEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>563</Char><CodeMapItemPath>FileAnalyzer#工具方法#ReleaseResources</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-29T15:21:45.1894768+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>FileAnalyzer\FileAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w></ProjectHistoryData><ProjectInCodeHighlightData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\LoggingHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\LoggingHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\IConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\IConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\PathPreviewForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\PathPreviewForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\ConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\ConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\StationInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\StationInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserMenuOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserMenuOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserTabManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerDownload.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookiePathManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfigForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\TabConfigForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCookieOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCookieOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>HyAssistantLicenseManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyAssistantLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\CookieManagerForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Main.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Main.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Program.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserResourceManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserResourceManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\IPhotoRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\IPhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerdownload.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>frmChinaTowerdownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\MenuGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\MenuGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserUIHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserUIHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\FormClosingHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\FormClosingHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\CookieManagerForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>VisioPDF\VisioPDF.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>VisioPDF\VisioPDF.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserStatusBridge.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserStatusBridge.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>HaUIPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HaUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionKeeper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserSessionKeeper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConstants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>FileCopier\FileCopier.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>FileCopier\FileCopier.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\frmChinaTowerDownload.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\StationRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserV2Constants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserV2Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowser.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserCookieManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserCookieManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieTransferManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieTransferManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FormClosingHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\FormClosingHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCacheOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCacheOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserExceptionHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserExceptionHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TabMenuManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TabMenuManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserUIOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserUIOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Main.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Main.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserSessionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\PhotoRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\PhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\TabConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\NewFolderManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\NewFolderManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>HaPermissionKeys.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HaPermissionKeys.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>FileAnalyzer\FileAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>FileAnalyzer\FileAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w></ProjectInCodeHighlightData><ProjectMiniViewData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\LoggingHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\LoggingHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\IConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\IConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\PathPreviewForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\PathPreviewForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\ConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\ConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookiePathLogicTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncStabilityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\StationInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\StationInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\OperationRollbackManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserMenuOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserMenuOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserTabManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerDownload.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookiePathManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookiePathManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfigForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\TabConfigForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCookieOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCookieOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationStabilityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>HyAssistantLicenseManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HyAssistantLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\CookieManagerForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserLoggingManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Main.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Main.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Program.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserResourceManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserResourceManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Models\PhotoInfoExcerpt.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2_NEW.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\RetryStrategyManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\IPhotoRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\IPhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\CookieManagerFormV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\CookieManagerFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>frmChinaTowerdownload.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>frmChinaTowerdownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\MenuGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\MenuGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserUIHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserUIHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\FormClosingHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\FormClosingHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\CookieManagerForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\CookieManagerForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>VisioPDF\VisioPDF.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>VisioPDF\VisioPDF.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserStatusBridge.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserStatusBridge.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>HaUIPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HaUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\IWebBrowserLoggingV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserResourceManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionKeeper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserSessionKeeper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConstants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>FileCopier\FileCopier.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>FileCopier\FileCopier.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\frmChinaTowerDownload.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\StationRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserV2Constants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserV2Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowser.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\TabConfigFormV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\TabConfigFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserCookieManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserCookieManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieTransferManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieTransferManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FormClosingHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\FormClosingHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Managers\WebBrowserSessionManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserCacheOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserCacheOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\CriticalOperationLoggerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ErrorHandlingTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Services\IChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserExceptionHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserExceptionHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\VersionComparisonTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Forms\InputDialogV2.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Forms\InputDialogV2.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TabMenuManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TabMenuManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Core\WebBrowserUIOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Core\WebBrowserUIOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\NewBugDetectionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\ChinaTowerDownload.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\ChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Services\ChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebView2ThreadSafeOperatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Main.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Main.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\FunctionalTestValidatorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\WebBrowserSessionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\WebBrowserSessionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ChinaTowerDownload\Data\PhotoRepository.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ChinaTowerDownload\Data\PhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\ThreadSafeHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\WebBrowserConstantsV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowser\TabConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowser\TabConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\AsyncOperationExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>NewFolder\NewFolderManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>NewFolder\NewFolderManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>HaPermissionKeys.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>HaPermissionKeys.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>FileAnalyzer\FileAnalyzer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>FileAnalyzer\FileAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>WebBrowserV2\Utils\TaskCompletionSourceHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w></ProjectMiniViewData><ProjectName>HyAssistant</ProjectName></ProjectData>