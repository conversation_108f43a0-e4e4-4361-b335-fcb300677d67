{"RootPath": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools", "ProjectFileName": "ExtensionsTools.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Common.Utility\\AES.cs"}, {"SourceFile": "Common.Utility\\class\\IntPtrEnumHelper.cs"}, {"SourceFile": "Common.Utility\\class\\struct.cs"}, {"SourceFile": "Common.Utility\\class\\Win32Window.cs"}, {"SourceFile": "Common.Utility\\FileOperate.cs"}, {"SourceFile": "Common.Utility\\HWInfo.cs"}, {"SourceFile": "Common.Utility\\ValidatorHelper.cs"}, {"SourceFile": "Common.Utility\\系统\\Enums.cs"}, {"SourceFile": "Common.Utility\\系统\\Structs.cs"}, {"SourceFile": "Common.Utility\\系统\\Win32API.cs.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\AIExcelAssistant.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Constants\\AIConstants.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Exceptions\\AIExceptions.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Interfaces\\IAIInterfaces.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Models\\AIDataModels.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Models\\AIRequestModels.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Models\\AIResponseModels.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Services\\Core\\AIClient.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Services\\Core\\AIDataExtractor.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Services\\Core\\AIFileProcessor.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Services\\Core\\AIProcessingManager.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Services\\Core\\AIResultFiller.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Utils\\AIConfigManager.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Utils\\AIErrorHandler.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Utils\\AILogger.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Utils\\FileValidationHelper.cs"}, {"SourceFile": "ExtensionsTools\\ETAIv2\\Utils\\JsonSchemaHelper.cs"}, {"SourceFile": "ExtensionsTools\\ETDateHelper.cs"}, {"SourceFile": "ExtensionsTools\\Controls\\ETLogDisplayControl.cs"}, {"SourceFile": "ExtensionsTools\\Controls\\ETLogDisplayControl.Designer.cs"}, {"SourceFile": "ExtensionsTools\\Controls\\ETRangeSelectControl.cs"}, {"SourceFile": "ExtensionsTools\\Controls\\ETRangeSelectControl.Designer.cs"}, {"SourceFile": "ExtensionsTools\\Controls\\IExcelApplicationProvider.cs"}, {"SourceFile": "ExtensionsTools\\ETGeographic\\ETPolygonUtil.cs"}, {"SourceFile": "ExtensionsTools\\ETInitializer.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETAboutLicenseForm.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETAboutLicenseForm.Designer.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETControlMappingGenerator.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETControlMappingManagerBase.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETControlPermissionManager.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseAdminLoginForm.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseAdminLoginForm.Designer.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseController.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseCryptoService.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseEntityForm.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseEntityForm.Designer.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseGenerator.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseGeneratorForm.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseGeneratorForm.Designer.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseManager.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseManagerBase.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseModels.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseProvider.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLicenseVerifier.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLocationService.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLocationTestForm.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETLocationTestForm.Designer.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETMachineCodeProvider.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETOssBrowserForm.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETOssBrowserForm.Designer.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETOssConfig.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETOssConfigForm.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETOssConfigForm.Designer.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETOssService.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETPermissionManager.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETPermissionNamesForm.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETPermissionNamesForm.Designer.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETThreadSafeCallbackHelper.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\ETUIPermissionManager.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\Extensions\\ETDictionaryExtensions.cs"}, {"SourceFile": "ExtensionsTools\\ETLicense\\InputBox.cs"}, {"SourceFile": "ExtensionsTools\\ETLoginWebBrowser\\ETLoginWebBrowser.cs"}, {"SourceFile": "ExtensionsTools\\ETLoginWebBrowser\\ETLoginWebBrowser.Designer.cs"}, {"SourceFile": "ExtensionsTools\\ETLoginWebBrowser\\ETLoginWebBrowserFactory.cs"}, {"SourceFile": "ExtensionsTools\\ETLoginWebBrowser\\ETWebBrowserJsonFormatter.cs"}, {"SourceFile": "ExtensionsTools\\ETLoginWebBrowser\\WebView2DiagnosticTool.cs"}, {"SourceFile": "ExtensionsTools\\ETLoginWebBrowser\\WebView2Helper.cs"}, {"SourceFile": "ExtensionsTools\\ETSectionConfigReader.cs"}, {"SourceFile": "ExtensionsTools\\ETTextCrypto.cs"}, {"SourceFile": "ExtensionsTools\\ETTools\\ETFileAnalyzer.cs"}, {"SourceFile": "ExtensionsTools\\ETAutoCollapseWindowBehavior.cs"}, {"SourceFile": "ExtensionsTools\\Controls\\ETAutoResetLabel.cs"}, {"SourceFile": "ExtensionsTools\\ETCommunicationService\\ETCommunicationService.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\FormManager\\Enums\\XlFormPosition.cs"}, {"SourceFile": "ExtensionsTools\\ETException.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\FormManager\\HHFormManager.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelAutoUpdate2.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelDataSummarizer.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_Initialize.cs"}, {"SourceFile": "ExtensionsTools\\ETTools\\ETFileCopier.cs"}, {"SourceFile": "ExtensionsTools\\ETIniFile.cs"}, {"SourceFile": "ExtensionsTools\\ETFormater.cs"}, {"SourceFile": "ExtensionsTools\\ETStock\\AkToolsClient.cs"}, {"SourceFile": "ExtensionsTools\\ETStock\\AkToolsClientStockDataCrawler.cs"}, {"SourceFile": "ExtensionsTools\\ETStock\\StockHelper.cs"}, {"SourceFile": "ExtensionsTools\\ETStock\\StockSoftwareCommand.cs"}, {"SourceFile": "ExtensionsTools\\ETString2.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_APP.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_Shape_Search.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_ShapeBase.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_Document.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_Shape2.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_图衔图框.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_Excel.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_Other.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_Page.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_PDF.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_Shape.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_Struct.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioHelper2013_Text.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\FormManager\\Interfaces\\IExcelSelectionChangeNotify.cs"}, {"SourceFile": "ExtensionsTools\\ETLogManager.cs"}, {"SourceFile": "ExtensionsTools\\ETGeographic\\ETGpsConvertUtil.cs"}, {"SourceFile": "ExtensionsTools\\ETConfig.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelAutoUpdate.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelConfig.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_AppControl.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_CellComments.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_Conversion.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_Core.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_DisplayNamesShapes.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_FilterAndHeader.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_Format.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_GetObjects.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_HiddenComments.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_InputDialog.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_Jump.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_Other.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_RangeOperations.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_RowColumnNumbers.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_StringProcessing.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_Struct.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_TagOperations.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_TextSearch.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_Validation.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_WorkbookOperations.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_WorksheetOperations.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_WorksheetProperties.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_WorksheetProtection.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_WPS.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_单元格填写.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_填写辅助操作.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_外部连接操作.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_字符处理操作.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_工作簿操作.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_工作表操作.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_常用操作.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_排序操作.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_格式操作.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_筛选操作.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_表格操作.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_转换操作.cs"}, {"SourceFile": "ExtensionsTools\\ETExcel\\ETExcelExtensions_页面设置操作.cs"}, {"SourceFile": "ExtensionsTools\\ETFile.cs"}, {"SourceFile": "ExtensionsTools\\ETForm.cs"}, {"SourceFile": "ExtensionsTools\\ETStringPrefixSuffixProcessor.cs"}, {"SourceFile": "ExtensionsTools\\WinAPI_form.cs"}, {"SourceFile": "ExtensionsTools\\ETGeographic\\ETGPS.cs"}, {"SourceFile": "ExtensionsTools\\ETNet.cs"}, {"SourceFile": "ExtensionsTools\\ETPowerPoint\\HHPowerPoint.cs"}, {"SourceFile": "ExtensionsTools\\ETString.cs"}, {"SourceFile": "ExtensionsTools\\ETTime.cs"}, {"SourceFile": "ExtensionsTools\\ETWord\\ETWord.cs"}, {"SourceFile": "ExtensionsTools\\ETTools\\ETNotificationHelper.cs"}, {"SourceFile": "ExtensionsTools\\Controls\\HHUcDirectorySelect.cs"}, {"SourceFile": "ExtensionsTools\\Controls\\HHUcDirectorySelect.Designer.cs"}, {"SourceFile": "ExtensionsTools\\Controls\\ETUcFileSelect.cs"}, {"SourceFile": "ExtensionsTools\\Controls\\ETUcFileSelect.Designer.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\ETVisioMonitor.cs"}, {"SourceFile": "ExtensionsTools\\ETVisio\\HHVisioToPdfConverter.cs"}, {"SourceFile": "ExtensionsTools\\WinAPI.cs"}, {"SourceFile": "ExtensionsTools\\ETTools\\ETWxPusherService.cs"}, {"SourceFile": "ExtensionsTools\\ETMail.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Aliyun.OSS.SDK.2.14.1\\lib\\net461\\Aliyun.OSS.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Bcl.AsyncInterfaces.9.0.6\\lib\\net462\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.5\\lib\\net462\\Microsoft.Extensions.DependencyInjection.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Extensions.Logging.Abstractions.9.0.5\\lib\\net462\\Microsoft.Extensions.Logging.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\Visual Studio Tools for Office\\PIA\\Office15\\Microsoft.Office.Interop.Excel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\Visual Studio Tools for Office\\PIA\\Office15\\Microsoft.Office.Interop.PowerPoint.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\Visual Studio Tools for Office\\PIA\\Office15\\Microsoft.Office.Interop.Visio.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\Visual Studio Tools for Office\\PIA\\Office15\\Microsoft.Office.Interop.Word.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.Common.v4.0.Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.Excel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.Excel.v4.0.Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.Word.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.Word.v4.0.Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.VisualBasic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Web.WebView2.1.0.3240.44\\lib\\net462\\Microsoft.Web.WebView2.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Web.WebView2.1.0.3240.44\\lib\\net462\\Microsoft.Web.WebView2.WinForms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Web.WebView2.1.0.3240.44\\lib\\net462\\Microsoft.Web.WebView2.Wpf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\NetTopologySuite.2.6.0\\lib\\netstandard2.0\\NetTopologySuite.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\Visual Studio Tools for Office\\PIA\\Office15\\Office.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\OpenAI.2.1.0\\lib\\netstandard2.0\\OpenAI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\SharpCompress.0.40.0\\lib\\net48\\SharpCompress.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Buffers.4.6.1\\lib\\net462\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.ClientModel.1.4.1\\lib\\netstandard2.0\\System.ClientModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Collections.Immutable.9.0.5\\lib\\net462\\System.Collections.Immutable.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ComponentModel.Composition.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Diagnostics.DiagnosticSource.9.0.5\\lib\\net462\\System.Diagnostics.DiagnosticSource.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.IO.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.IO.Pipelines.9.0.6\\lib\\net462\\System.IO.Pipelines.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Memory.Data.9.0.6\\lib\\net462\\System.Memory.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Memory.4.6.3\\lib\\net462\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Numerics.Vectors.4.6.1\\lib\\net462\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Runtime.CompilerServices.Unsafe.6.1.2\\lib\\net462\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.Algorithms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.Encoding.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.X509Certificates.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ServiceModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Text.Encodings.Web.9.0.6\\lib\\net462\\System.Text.Encodings.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Text.Json.9.0.6\\lib\\net462\\System.Text.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Threading.Tasks.Extensions.4.6.3\\lib\\net462\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\ZstdSharp.Port.0.8.5\\lib\\net462\\ZstdSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\bin\\Debug\\ExtensionsTools.dll", "OutputItemRelativePath": "ExtensionsTools.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}