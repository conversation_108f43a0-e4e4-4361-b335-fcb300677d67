﻿using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Windows.Forms;
using Excel = Microsoft.Office.Interop.Excel;


namespace HyExcelVsto.Module.Common
{

    /// <summary>
    /// Excel工作表页眉页脚设置窗体
    /// 建议英文命名：HeaderFooterSettingsForm
    /// </summary>
    public partial class frm设置页眉页脚 : Form
    {
        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        public Excel.Application XlApp;

        /// <summary>
        /// 初始化页眉页脚设置窗体
        /// </summary>
        public frm设置页眉页脚()
        {
            InitializeComponent();
            XlApp = Globals.ThisAddIn.Application;
        }

        /// <summary>
        /// 窗体加载事件，初始化默认的页脚内容并绑定ComboBox历史记录
        /// </summary>
        void frm设置页眉页脚_Load(object sender, EventArgs e)
        {
            try
            {
                // 绑定ComboBox历史记录功能
                ETForm.BindComboBox(comboBox页眉左);
                ETForm.BindComboBox(comboBox页眉中);
                ETForm.BindComboBox(comboBox页眉右);
                ETForm.BindComboBox(comboBox页脚左);
                ETForm.BindComboBox(comboBox页脚中);
                ETForm.BindComboBox(comboBox页脚右);

                // 设置默认值（如果ComboBox为空）
                if (string.IsNullOrEmpty(comboBox页脚左.Text))
                    comboBox页脚左.Text = @"设计负责人:xxx";
                if (string.IsNullOrEmpty(comboBox页脚中.Text))
                    comboBox页脚中.Text = @"审核:xxx                    编制:xxx";
                if (string.IsNullOrEmpty(comboBox页脚右.Text))
                    comboBox页脚右.Text = $@"编制日期:{DateTime.Now.ToString("yyyy年M月")}";
            }
            catch (Exception ex)
            {
                throw new ETException("初始化页眉页脚窗体失败", "窗体初始化", ex);
            }
        }

        /// <summary>
        /// 设置页眉页脚按钮点击事件
        /// </summary>
        void button设置页眉页脚_Click(object sender, EventArgs e)
        {
            try
            {
                DialogResult userConfirmation = MessageBox.Show(@"确定要设置页眉页码?", @"提示", MessageBoxButtons.YesNo);
                if (userConfirmation != DialogResult.Yes) return;

                foreach (Worksheet worksheet in XlApp.ActiveWindow.SelectedSheets)
                {
                    try
                    {
                        ApplyHeaderFooterToWorksheet(worksheet);
                    }
                    catch (Exception sheetEx)
                    {
                        throw new ETException($"设置工作表 {worksheet.Name} 的页眉页脚失败", "页眉页脚设置", sheetEx);
                    }
                }

                Close();
                Dispose();
            }
            catch (Exception ex)
            {
                throw new ETException("设置页眉页脚失败", "页眉页脚设置", ex);
            }
        }

        /// <summary>
        /// 为指定工作表应用页眉页脚设置
        /// </summary>
        void ApplyHeaderFooterToWorksheet(Worksheet worksheet)
        {
            try
            {
                worksheet.PageSetup.LeftFooter = comboBox页脚左.Text;
                worksheet.PageSetup.CenterFooter = comboBox页脚中.Text;
                worksheet.PageSetup.RightFooter = comboBox页脚右.Text;

                worksheet.PageSetup.LeftHeader = comboBox页眉左.Text;
                worksheet.PageSetup.CenterHeader = comboBox页眉中.Text;
                worksheet.PageSetup.RightHeader = comboBox页眉右.Text;
            }
            catch (Exception ex)
            {
                throw new ETException("设置页眉页脚属性失败", "页眉页脚设置", ex);
            }
        }
    }
}