using System;
using System.Windows.Forms;
using ET.Controls;
using ET;

namespace ExtensionsTools.Controls
{
    /// <summary>
    /// ETRangeSelectControl WPS调试测试窗体
    /// 用于诊断WPS环境下的应用程序获取问题
    /// </summary>
    public partial class ETRangeSelectControlDebugForm : Form
    {
        private ETRangeSelectControl etRangeSelectControl;
        private Button btnDebugTest;
        private Button btnTestSelection;
        private TextBox txtLog;
        private Label lblStatus;

        public ETRangeSelectControlDebugForm()
        {
            InitializeComponent();
            InitializeControls();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(600, 500);
            this.Text = "ETRangeSelectControl WPS调试测试";
            this.StartPosition = FormStartPosition.CenterScreen;
            
            this.ResumeLayout(false);
        }

        private void InitializeControls()
        {
            // ETRangeSelectControl
            etRangeSelectControl = new ETRangeSelectControl();
            etRangeSelectControl.Location = new System.Drawing.Point(12, 12);
            etRangeSelectControl.Size = new System.Drawing.Size(300, 21);
            etRangeSelectControl.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.Controls.Add(etRangeSelectControl);

            // 状态标签
            lblStatus = new Label();
            lblStatus.Location = new System.Drawing.Point(12, 40);
            lblStatus.Size = new System.Drawing.Size(576, 20);
            lblStatus.Text = "状态：等待测试";
            lblStatus.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.Controls.Add(lblStatus);

            // 调试测试按钮
            btnDebugTest = new Button();
            btnDebugTest.Location = new System.Drawing.Point(12, 70);
            btnDebugTest.Size = new System.Drawing.Size(120, 30);
            btnDebugTest.Text = "运行调试测试";
            btnDebugTest.UseVisualStyleBackColor = true;
            btnDebugTest.Click += BtnDebugTest_Click;
            this.Controls.Add(btnDebugTest);

            // 测试选择按钮
            btnTestSelection = new Button();
            btnTestSelection.Location = new System.Drawing.Point(140, 70);
            btnTestSelection.Size = new System.Drawing.Size(120, 30);
            btnTestSelection.Text = "测试Range选择";
            btnTestSelection.UseVisualStyleBackColor = true;
            btnTestSelection.Click += BtnTestSelection_Click;
            this.Controls.Add(btnTestSelection);

            // 日志文本框
            txtLog = new TextBox();
            txtLog.Location = new System.Drawing.Point(12, 110);
            txtLog.Size = new System.Drawing.Size(576, 378);
            txtLog.Multiline = true;
            txtLog.ScrollBars = ScrollBars.Vertical;
            txtLog.ReadOnly = true;
            txtLog.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            txtLog.Font = new System.Drawing.Font("Consolas", 9F);
            this.Controls.Add(txtLog);

            // 绑定事件
            etRangeSelectControl.SelectedEvent += EtRangeSelectControl_SelectedEvent;
            etRangeSelectControl.BeginSelectEvent += EtRangeSelectControl_BeginSelectEvent;
        }

        private void BtnDebugTest_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "状态：正在运行调试测试...";
                txtLog.Clear();
                
                AppendLog("=== ETRangeSelectControl WPS调试测试开始 ===");
                AppendLog($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                AppendLog("");

                // 运行调试测试
                etRangeSelectControl.DebugTestAllApplicationGetters();
                
                AppendLog("");
                AppendLog("=== 调试测试完成 ===");
                lblStatus.Text = "状态：调试测试完成，请查看日志";
            }
            catch (Exception ex)
            {
                AppendLog($"调试测试异常: {ex.Message}");
                AppendLog($"堆栈跟踪: {ex.StackTrace}");
                lblStatus.Text = "状态：调试测试异常";
            }
        }

        private void BtnTestSelection_Click(object sender, EventArgs e)
        {
            try
            {
                lblStatus.Text = "状态：测试Range选择功能...";
                AppendLog("=== 测试Range选择功能 ===");
                
                // 这里会触发控件的选择功能
                // 实际的选择会通过用户点击控件的选择按钮来触发
                AppendLog("请点击ETRangeSelectControl右侧的选择按钮来测试Range选择功能");
                
                lblStatus.Text = "状态：等待用户操作Range选择";
            }
            catch (Exception ex)
            {
                AppendLog($"测试Range选择异常: {ex.Message}");
                lblStatus.Text = "状态：Range选择测试异常";
            }
        }

        private void EtRangeSelectControl_SelectedEvent(object sender, EventArgs e)
        {
            try
            {
                var range = etRangeSelectControl.SelectedRange;
                if (range != null)
                {
                    AppendLog($"✅ Range选择成功: {range.Address}");
                    AppendLog($"   工作表: {range.Worksheet.Name}");
                    AppendLog($"   完整地址: {etRangeSelectControl.FullSelectedAddress}");
                    lblStatus.Text = "状态：Range选择成功";
                }
                else
                {
                    AppendLog("❌ Range选择失败或用户取消");
                    lblStatus.Text = "状态：Range选择失败";
                }
            }
            catch (Exception ex)
            {
                AppendLog($"处理Range选择事件异常: {ex.Message}");
                lblStatus.Text = "状态：Range选择事件处理异常";
            }
        }

        private void EtRangeSelectControl_BeginSelectEvent(object sender, EventArgs e)
        {
            AppendLog("🔄 开始Range选择...");
            lblStatus.Text = "状态：正在进行Range选择";
        }

        private void AppendLog(string message)
        {
            if (txtLog.InvokeRequired)
            {
                txtLog.Invoke(new Action<string>(AppendLog), message);
                return;
            }

            txtLog.AppendText($"[{DateTime.Now:HH:mm:ss}] {message}\r\n");
            txtLog.SelectionStart = txtLog.Text.Length;
            txtLog.ScrollToCaret();
        }
    }
}
