﻿namespace  HyExcelVsto
{
    partial class HyRibbonClass : Microsoft.Office.Tools.Ribbon.RibbonBase
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        public HyRibbonClass()
            : base(Globals.Factory.GetRibbonFactory())
        {
            InitializeComponent();
        }

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.hyTab = this.Factory.CreateRibbonTab();
            this.group字符格式 = this.Factory.CreateRibbonGroup();
            this.btn标记提取规整字符串b = this.Factory.CreateRibbonButton();
            this.btn填写合规检查 = this.Factory.CreateRibbonButton();
            this.button向下填充 = this.Factory.CreateRibbonButton();
            this.separator7 = this.Factory.CreateRibbonSeparator();
            this.button外部链接 = this.Factory.CreateRibbonButton();
            this.btn设置页眉脚 = this.Factory.CreateRibbonButton();
            this.group标记标签 = this.Factory.CreateRibbonGroup();
            this.btn批量查找 = this.Factory.CreateRibbonButton();
            this.button标签填写筛选 = this.Factory.CreateRibbonButton();
            this.separator8 = this.Factory.CreateRibbonSeparator();
            this.button取消条件格式并取消筛选 = this.Factory.CreateRibbonButton();
            this.button清除所选条件格式 = this.Factory.CreateRibbonButton();
            this.button清除全表条件格式 = this.Factory.CreateRibbonButton();
            this.group数据处理 = this.Factory.CreateRibbonGroup();
            this.button同步数据 = this.Factory.CreateRibbonButton();
            this.buttonAI辅助填写 = this.Factory.CreateRibbonButton();
            this.groupOffice = this.Factory.CreateRibbonGroup();
            this.buttonPPTHelper = this.Factory.CreateRibbonButton();
            this.buttonWordHelper = this.Factory.CreateRibbonButton();
            this.buttonVisioHelper = this.Factory.CreateRibbonButton();
            this.group文件 = this.Factory.CreateRibbonGroup();
            this.btn发送及存档 = this.Factory.CreateRibbonButton();
            this.gallery常用文件 = this.Factory.CreateRibbonGallery();
            this.button文件操作 = this.Factory.CreateRibbonButton();
            this.separator12 = this.Factory.CreateRibbonSeparator();
            this.button5 = this.Factory.CreateRibbonButton();
            this.button记录当前文件 = this.Factory.CreateRibbonButton();
            this.button复制当前文件路径 = this.Factory.CreateRibbonButton();
            this.separator3 = this.Factory.CreateRibbonSeparator();
            this.buttonWpsExcel切换 = this.Factory.CreateRibbonButton();
            this.menu其它3 = this.Factory.CreateRibbonMenu();
            this.btm工作表管理 = this.Factory.CreateRibbonButton();
            this.group无线 = this.Factory.CreateRibbonGroup();
            this.button通过GPS计算最近站点 = this.Factory.CreateRibbonButton();
            this.button生成地理图层 = this.Factory.CreateRibbonButton();
            this.btn格式化经纬度 = this.Factory.CreateRibbonButton();
            this.button专用工具 = this.Factory.CreateRibbonButton();
            this.button51ToolsV1b = this.Factory.CreateRibbonButton();
            this.menu1 = this.Factory.CreateRibbonMenu();
            this.button8 = this.Factory.CreateRibbonButton();
            this.separator2 = this.Factory.CreateRibbonSeparator();
            this.buttonDevelopTest = this.Factory.CreateRibbonButton();
            this.button51ToolsV2b = this.Factory.CreateRibbonButton();
            this.group2 = this.Factory.CreateRibbonGroup();
            this.btn自动脚本 = this.Factory.CreateRibbonButton();
            this.button打开脚本表 = this.Factory.CreateRibbonButton();
            this.gallery脚本内容 = this.Factory.CreateRibbonGallery();
            this.hy_group其它 = this.Factory.CreateRibbonGroup();
            this.checkBox叠加显示辅助 = this.Factory.CreateRibbonCheckBox();
            this.checkBox监控剪贴板 = this.Factory.CreateRibbonCheckBox();
            this.menuHY = this.Factory.CreateRibbonMenu();
            this.checkBoxStockHelper = this.Factory.CreateRibbonCheckBox();
            this.button考勤 = this.Factory.CreateRibbonButton();
            this.separator6 = this.Factory.CreateRibbonSeparator();
            this.menu修复 = this.Factory.CreateRibbonMenu();
            this.button6 = this.Factory.CreateRibbonButton();
            this.button重置单元格备注大小 = this.Factory.CreateRibbonButton();
            this.hy_menu设置 = this.Factory.CreateRibbonMenu();
            this.chk显示0值 = this.Factory.CreateRibbonCheckBox();
            this.checkBox分级标记 = this.Factory.CreateRibbonCheckBox();
            this.separator1 = this.Factory.CreateRibbonSeparator();
            this.buttonini配置文件 = this.Factory.CreateRibbonButton();
            this.button配置目录 = this.Factory.CreateRibbonButton();
            this.separator5 = this.Factory.CreateRibbonSeparator();
            this.buttonAboutHy = this.Factory.CreateRibbonButton();
            this.group1 = this.Factory.CreateRibbonGroup();
            this.button2 = this.Factory.CreateRibbonButton();
            this.button3 = this.Factory.CreateRibbonButton();
            this.znTab = this.Factory.CreateRibbonTab();
            this.zn_group字符格式 = this.Factory.CreateRibbonGroup();
            this.btn标记提取规整字符串a = this.Factory.CreateRibbonButton();
            this.btn填写合规性检查abc = this.Factory.CreateRibbonButton();
            this.button17 = this.Factory.CreateRibbonButton();
            this.separator9 = this.Factory.CreateRibbonSeparator();
            this.button11 = this.Factory.CreateRibbonButton();
            this.button12 = this.Factory.CreateRibbonButton();
            this.button13 = this.Factory.CreateRibbonButton();
            this.zn_groupOffice = this.Factory.CreateRibbonGroup();
            this.buttonPPT生成修改转PDF_B = this.Factory.CreateRibbonButton();
            this.buttonWord生成修改转PDF_B = this.Factory.CreateRibbonButton();
            this.zn_group文件 = this.Factory.CreateRibbonGroup();
            this.button14 = this.Factory.CreateRibbonButton();
            this.button批量找文件 = this.Factory.CreateRibbonButton();
            this.separator11 = this.Factory.CreateRibbonSeparator();
            this.button9 = this.Factory.CreateRibbonButton();
            this.button7 = this.Factory.CreateRibbonButton();
            this.zn_group无线 = this.Factory.CreateRibbonGroup();
            this.button16 = this.Factory.CreateRibbonButton();
            this.button23 = this.Factory.CreateRibbonButton();
            this.button24 = this.Factory.CreateRibbonButton();
            this.button15 = this.Factory.CreateRibbonButton();
            this.button51ToolsV1 = this.Factory.CreateRibbonButton();
            this.zn_group其它 = this.Factory.CreateRibbonGroup();
            this.checkBoxVerticalHighlight = this.Factory.CreateRibbonCheckBox();
            this.checkBoxHorizontalHighlight = this.Factory.CreateRibbonCheckBox();
            this.separator10 = this.Factory.CreateRibbonSeparator();
            this.menu3 = this.Factory.CreateRibbonMenu();
            this.button1 = this.Factory.CreateRibbonButton();
            this.button4 = this.Factory.CreateRibbonButton();
            this.menu5 = this.Factory.CreateRibbonMenu();
            this.button20 = this.Factory.CreateRibbonButton();
            this.button26 = this.Factory.CreateRibbonButton();
            this.buttonAboutZn = this.Factory.CreateRibbonButton();
            this.znAbout = this.Factory.CreateRibbonTab();
            this.znAboutGroup = this.Factory.CreateRibbonGroup();
            this.znAboutButton = this.Factory.CreateRibbonButton();
            this.menu设置其它 = this.Factory.CreateRibbonMenu();
            this.btn设置倍数行高 = this.Factory.CreateRibbonButton();
            this.separator4 = this.Factory.CreateRibbonSeparator();
            this.btn金额转大写 = this.Factory.CreateRibbonButton();
            this.btn隐藏范围外内容 = this.Factory.CreateRibbonButton();
            this.hyTab.SuspendLayout();
            this.group字符格式.SuspendLayout();
            this.group标记标签.SuspendLayout();
            this.group数据处理.SuspendLayout();
            this.groupOffice.SuspendLayout();
            this.group文件.SuspendLayout();
            this.group无线.SuspendLayout();
            this.group2.SuspendLayout();
            this.hy_group其它.SuspendLayout();
            this.group1.SuspendLayout();
            this.znTab.SuspendLayout();
            this.zn_group字符格式.SuspendLayout();
            this.zn_groupOffice.SuspendLayout();
            this.zn_group文件.SuspendLayout();
            this.zn_group无线.SuspendLayout();
            this.zn_group其它.SuspendLayout();
            this.znAbout.SuspendLayout();
            this.znAboutGroup.SuspendLayout();
            this.SuspendLayout();
            // 
            // hyTab
            // 
            this.hyTab.Groups.Add(this.group字符格式);
            this.hyTab.Groups.Add(this.group标记标签);
            this.hyTab.Groups.Add(this.group数据处理);
            this.hyTab.Groups.Add(this.groupOffice);
            this.hyTab.Groups.Add(this.group文件);
            this.hyTab.Groups.Add(this.group无线);
            this.hyTab.Groups.Add(this.group2);
            this.hyTab.Groups.Add(this.hy_group其它);
            this.hyTab.Label = "Develop";
            this.hyTab.Name = "hyTab";
            // 
            // group字符格式
            // 
            this.group字符格式.Items.Add(this.btn标记提取规整字符串b);
            this.group字符格式.Items.Add(this.btn填写合规检查);
            this.group字符格式.Items.Add(this.button向下填充);
            this.group字符格式.Items.Add(this.separator7);
            this.group字符格式.Items.Add(this.button外部链接);
            this.group字符格式.Items.Add(this.btn设置页眉脚);
            this.group字符格式.Items.Add(this.menu设置其它);
            this.group字符格式.Label = "字符/格式";
            this.group字符格式.Name = "group字符格式";
            // 
            // btn标记提取规整字符串b
            // 
            this.btn标记提取规整字符串b.Label = "标记/提取/规整字符";
            this.btn标记提取规整字符串b.Name = "btn标记提取规整字符串b";
            this.btn标记提取规整字符串b.OfficeImageId = "DropCapOptionsDialog";
            this.btn标记提取规整字符串b.ShowImage = true;
            this.btn标记提取规整字符串b.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy字符串处理_Click);
            // 
            // btn填写合规检查
            // 
            this.btn填写合规检查.Label = "填写合规性检查";
            this.btn填写合规检查.Name = "btn填写合规检查";
            this.btn填写合规检查.OfficeImageId = "SlideMasterTextPlaceholderInsert";
            this.btn填写合规检查.ShowImage = true;
            this.btn填写合规检查.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy填写合规检查_Click);
            // 
            // button向下填充
            // 
            this.button向下填充.Label = "向下填充";
            this.button向下填充.Name = "button向下填充";
            this.button向下填充.OfficeImageId = "ConvertTextToTable";
            this.button向下填充.ShowImage = true;
            this.button向下填充.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHy向下填充_Click);
            // 
            // separator7
            // 
            this.separator7.Name = "separator7";
            // 
            // button外部链接
            // 
            this.button外部链接.Label = "删除外部链接";
            this.button外部链接.Name = "button外部链接";
            this.button外部链接.OfficeImageId = "HyperlinkInsert";
            this.button外部链接.ShowImage = true;
            this.button外部链接.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy删除外部链接_Click);
            // 
            // btn设置页眉脚
            // 
            this.btn设置页眉脚.Label = "设置页眉脚";
            this.btn设置页眉脚.Name = "btn设置页眉脚";
            this.btn设置页眉脚.OfficeImageId = "PageSetup";
            this.btn设置页眉脚.ShowImage = true;
            this.btn设置页眉脚.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy设置页眉脚_Click);
            // 
            // group标记标签
            // 
            this.group标记标签.Items.Add(this.btn批量查找);
            this.group标记标签.Items.Add(this.button标签填写筛选);
            this.group标记标签.Items.Add(this.separator8);
            this.group标记标签.Items.Add(this.button取消条件格式并取消筛选);
            this.group标记标签.Items.Add(this.button清除所选条件格式);
            this.group标记标签.Items.Add(this.button清除全表条件格式);
            this.group标记标签.Label = "标记标签";
            this.group标记标签.Name = "group标记标签";
            // 
            // btn批量查找
            // 
            this.btn批量查找.Label = "批量查找";
            this.btn批量查找.Name = "btn批量查找";
            this.btn批量查找.OfficeImageId = "DatasheetColumnLookup";
            this.btn批量查找.ShowImage = true;
            this.btn批量查找.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy批量查找_Click);
            // 
            // button标签填写筛选
            // 
            this.button标签填写筛选.Label = "标签填写/筛选";
            this.button标签填写筛选.Name = "button标签填写筛选";
            this.button标签填写筛选.OfficeImageId = "Filter";
            this.button标签填写筛选.ShowImage = true;
            this.button标签填写筛选.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHy标签填写筛选_Click);
            // 
            // separator8
            // 
            this.separator8.Name = "separator8";
            // 
            // button取消条件格式并取消筛选
            // 
            this.button取消条件格式并取消筛选.Label = "清除所选条件格式及筛选";
            this.button取消条件格式并取消筛选.Name = "button取消条件格式并取消筛选";
            this.button取消条件格式并取消筛选.OfficeImageId = "Clear";
            this.button取消条件格式并取消筛选.ShowImage = true;
            this.button取消条件格式并取消筛选.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy清除所选条件格式及全表筛选_Click);
            // 
            // button清除所选条件格式
            // 
            this.button清除所选条件格式.Label = "清除所选条件格式";
            this.button清除所选条件格式.Name = "button清除所选条件格式";
            this.button清除所选条件格式.OfficeImageId = "InkEraser";
            this.button清除所选条件格式.ShowImage = true;
            this.button清除所选条件格式.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy清除所选条件格式_Click);
            // 
            // button清除全表条件格式
            // 
            this.button清除全表条件格式.Label = "清除全表条件格式";
            this.button清除全表条件格式.Name = "button清除全表条件格式";
            this.button清除全表条件格式.OfficeImageId = "ClearFormatting";
            this.button清除全表条件格式.ShowImage = true;
            this.button清除全表条件格式.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonyHy清除全表条件格式_Click);
            // 
            // group数据处理
            // 
            this.group数据处理.Items.Add(this.button同步数据);
            this.group数据处理.Items.Add(this.buttonAI辅助填写);
            this.group数据处理.Label = "数据处理";
            this.group数据处理.Name = "group数据处理";
            // 
            // button同步数据
            // 
            this.button同步数据.Label = "同步数据";
            this.button同步数据.Name = "button同步数据";
            this.button同步数据.OfficeImageId = "Refresh";
            this.button同步数据.ShowImage = true;
            this.button同步数据.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHy同步数据_Click);
            // 
            // buttonAI辅助填写
            // 
            this.buttonAI辅助填写.Label = "AI辅助填写";
            this.buttonAI辅助填写.Name = "buttonAI辅助填写";
            this.buttonAI辅助填写.OfficeImageId = "AutoSum";
            this.buttonAI辅助填写.ShowImage = true;
            this.buttonAI辅助填写.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHyAI辅助填写_Click);
            // 
            // groupOffice
            // 
            this.groupOffice.Items.Add(this.buttonPPTHelper);
            this.groupOffice.Items.Add(this.buttonWordHelper);
            this.groupOffice.Items.Add(this.buttonVisioHelper);
            this.groupOffice.Label = "Office";
            this.groupOffice.Name = "groupOffice";
            // 
            // buttonPPTHelper
            // 
            this.buttonPPTHelper.Label = "PPT助手";
            this.buttonPPTHelper.Name = "buttonPPTHelper";
            this.buttonPPTHelper.OfficeImageId = "FileSaveAsPowerPoint97_2003";
            this.buttonPPTHelper.ShowImage = true;
            this.buttonPPTHelper.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHyPPT助手_Click);
            // 
            // buttonWordHelper
            // 
            this.buttonWordHelper.Label = "Word助手";
            this.buttonWordHelper.Name = "buttonWordHelper";
            this.buttonWordHelper.OfficeImageId = "FileSaveAsWord97_2003";
            this.buttonWordHelper.ShowImage = true;
            this.buttonWordHelper.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHyWord助手_Click);
            // 
            // buttonVisioHelper
            // 
            this.buttonVisioHelper.Label = "Visio助手";
            this.buttonVisioHelper.Name = "buttonVisioHelper";
            this.buttonVisioHelper.OfficeImageId = "ShapeRectangle";
            this.buttonVisioHelper.ShowImage = true;
            this.buttonVisioHelper.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHyVisioHelper_Click);
            // 
            // group文件
            // 
            this.group文件.Items.Add(this.btn发送及存档);
            this.group文件.Items.Add(this.gallery常用文件);
            this.group文件.Items.Add(this.button文件操作);
            this.group文件.Items.Add(this.separator12);
            this.group文件.Items.Add(this.button5);
            this.group文件.Items.Add(this.button记录当前文件);
            this.group文件.Items.Add(this.button复制当前文件路径);
            this.group文件.Items.Add(this.separator3);
            this.group文件.Items.Add(this.buttonWpsExcel切换);
            this.group文件.Items.Add(this.menu其它3);
            this.group文件.Label = "文件";
            this.group文件.Name = "group文件";
            // 
            // btn发送及存档
            // 
            this.btn发送及存档.Label = "临时/发送/存档";
            this.btn发送及存档.Name = "btn发送及存档";
            this.btn发送及存档.OfficeImageId = "FileSaveAs";
            this.btn发送及存档.ShowImage = true;
            this.btn发送及存档.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy发送及存档_Click);
            // 
            // gallery常用文件
            // 
            this.gallery常用文件.ColumnCount = 1;
            this.gallery常用文件.Label = "常用文件";
            this.gallery常用文件.Name = "gallery常用文件";
            this.gallery常用文件.OfficeImageId = "FileOpen";
            this.gallery常用文件.ShowImage = true;
            this.gallery常用文件.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.Button常用文件_Click);
            this.gallery常用文件.ItemsLoading += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.gallery常用文件_ItemsLoading);
            // 
            // button文件操作
            // 
            this.button文件操作.Label = "文件查找/复制/改名";
            this.button文件操作.Name = "button文件操作";
            this.button文件操作.OfficeImageId = "SourceControlShowDifferences";
            this.button文件操作.ShowImage = true;
            this.button文件操作.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHy文件助手_Click);
            // 
            // separator12
            // 
            this.separator12.Name = "separator12";
            // 
            // button5
            // 
            this.button5.Label = "最近打开文件";
            this.button5.Name = "button5";
            this.button5.OfficeImageId = "FileOpen";
            this.button5.ShowImage = true;
            this.button5.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy最近打开文件_Click);
            // 
            // button记录当前文件
            // 
            this.button记录当前文件.Label = "记录当前文件";
            this.button记录当前文件.Name = "button记录当前文件";
            this.button记录当前文件.OfficeImageId = "FileSaveAs";
            this.button记录当前文件.ShowImage = true;
            this.button记录当前文件.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button记录当前文件_Click);
            // 
            // button复制当前文件路径
            // 
            this.button复制当前文件路径.Label = "复制路径";
            this.button复制当前文件路径.Name = "button复制当前文件路径";
            this.button复制当前文件路径.OfficeImageId = "Copy";
            this.button复制当前文件路径.ShowImage = true;
            this.button复制当前文件路径.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHy复制当前文件路径_Click);
            // 
            // separator3
            // 
            this.separator3.Name = "separator3";
            // 
            // buttonWpsExcel切换
            // 
            this.buttonWpsExcel切换.Label = "Wps/Excel切换";
            this.buttonWpsExcel切换.Name = "buttonWpsExcel切换";
            this.buttonWpsExcel切换.OfficeImageId = "WindowSwitchWindowsMenuExcel";
            this.buttonWpsExcel切换.ShowImage = true;
            this.buttonWpsExcel切换.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHyWpsExcel切换_Click);
            // 
            // menu其它3
            // 
            this.menu其它3.Items.Add(this.btm工作表管理);
            this.menu其它3.Label = "其它";
            this.menu其它3.Name = "menu其它3";
            this.menu其它3.OfficeImageId = "ActiveXComboBox";
            this.menu其它3.ShowImage = true;
            // 
            // btm工作表管理
            // 
            this.btm工作表管理.Label = "工作表管理";
            this.btm工作表管理.Name = "btm工作表管理";
            this.btm工作表管理.OfficeImageId = "SheetInsert";
            this.btm工作表管理.ShowImage = true;
            this.btm工作表管理.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button工作表管理_Click);
            // 
            // group无线
            // 
            this.group无线.Items.Add(this.button通过GPS计算最近站点);
            this.group无线.Items.Add(this.button生成地理图层);
            this.group无线.Items.Add(this.btn格式化经纬度);
            this.group无线.Items.Add(this.button专用工具);
            this.group无线.Items.Add(this.button51ToolsV1b);
            this.group无线.Items.Add(this.menu1);
            this.group无线.Label = "无线";
            this.group无线.Name = "group无线";
            // 
            // button通过GPS计算最近站点
            // 
            this.button通过GPS计算最近站点.Label = "批量查找站点";
            this.button通过GPS计算最近站点.Name = "button通过GPS计算最近站点";
            this.button通过GPS计算最近站点.OfficeImageId = "FileWorkflowTasks";
            this.button通过GPS计算最近站点.ShowImage = true;
            this.button通过GPS计算最近站点.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHy批量查找站点_Click);
            // 
            // button生成地理图层
            // 
            this.button生成地理图层.Label = "生成地理图层";
            this.button生成地理图层.Name = "button生成地理图层";
            this.button生成地理图层.OfficeImageId = "PictureReflectionGallery";
            this.button生成地理图层.ShowImage = true;
            this.button生成地理图层.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy生成地理图层_Click);
            // 
            // btn格式化经纬度
            // 
            this.btn格式化经纬度.Label = "经纬度工具";
            this.btn格式化经纬度.Name = "btn格式化经纬度";
            this.btn格式化经纬度.OfficeImageId = "WordCount";
            this.btn格式化经纬度.ShowImage = true;
            this.btn格式化经纬度.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy格式化经纬度_Click);
            // 
            // button专用工具
            // 
            this.button专用工具.Label = "专用工具";
            this.button专用工具.Name = "button专用工具";
            this.button专用工具.OfficeImageId = "DataFormSource";
            this.button专用工具.ShowImage = true;
            this.button专用工具.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHy专用工具_Click);
            // 
            // button51ToolsV1b
            // 
            this.button51ToolsV1b.Label = "51助手";
            this.button51ToolsV1b.Name = "button51ToolsV1b";
            this.button51ToolsV1b.OfficeImageId = "PivotTableLayoutShowInOutlineForm";
            this.button51ToolsV1b.ShowImage = true;
            this.button51ToolsV1b.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy51小工具V1_Click);
            // 
            // menu1
            // 
            this.menu1.Items.Add(this.button8);
            this.menu1.Items.Add(this.separator2);
            this.menu1.Items.Add(this.buttonDevelopTest);
            this.menu1.Items.Add(this.button51ToolsV2b);
            this.menu1.Label = "其它";
            this.menu1.Name = "menu1";
            this.menu1.OfficeImageId = "ActiveXComboBox";
            this.menu1.ShowImage = true;
            // 
            // button8
            // 
            this.button8.Label = "订单文件生成kml图层";
            this.button8.Name = "button8";
            this.button8.OfficeImageId = "PictureInsert";
            this.button8.ShowImage = true;
            this.button8.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button订单文件生成kml图层_Click);
            // 
            // separator2
            // 
            this.separator2.Name = "separator2";
            // 
            // buttonDevelopTest
            // 
            this.buttonDevelopTest.Label = "Test";
            this.buttonDevelopTest.Name = "buttonDevelopTest";
            this.buttonDevelopTest.OfficeImageId = "MacroPlay";
            this.buttonDevelopTest.ShowImage = true;
            this.buttonDevelopTest.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonDevelopTest_Click);
            // 
            // button51ToolsV2b
            // 
            this.button51ToolsV2b.Label = "51小工具v2";
            this.button51ToolsV2b.Name = "button51ToolsV2b";
            this.button51ToolsV2b.OfficeImageId = "Calculator";
            this.button51ToolsV2b.ShowImage = true;
            this.button51ToolsV2b.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHy51小工具V2_Click);
            // 
            // group2
            // 
            this.group2.Items.Add(this.btn自动脚本);
            this.group2.Items.Add(this.button打开脚本表);
            this.group2.Items.Add(this.gallery脚本内容);
            this.group2.Label = "脚本";
            this.group2.Name = "group2";
            // 
            // btn自动脚本
            // 
            this.btn自动脚本.Label = "自动脚本";
            this.btn自动脚本.Name = "btn自动脚本";
            this.btn自动脚本.OfficeImageId = "MacroPlay";
            this.btn自动脚本.ShowImage = true;
            this.btn自动脚本.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHy自动脚本_Click);
            // 
            // button打开脚本表
            // 
            this.button打开脚本表.Label = "打开脚本";
            this.button打开脚本表.Name = "button打开脚本表";
            this.button打开脚本表.OfficeImageId = "FileOpen";
            this.button打开脚本表.ShowImage = true;
            this.button打开脚本表.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button打开脚本表_Click);
            // 
            // gallery脚本内容
            // 
            this.gallery脚本内容.ColumnCount = 1;
            this.gallery脚本内容.Label = "脚本内容";
            this.gallery脚本内容.Name = "gallery脚本内容";
            this.gallery脚本内容.OfficeImageId = "MacroRecord";
            this.gallery脚本内容.ShowImage = true;
            this.gallery脚本内容.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.gallery脚本内容_Click);
            this.gallery脚本内容.ItemsLoading += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.gallery脚本内容_ItemsLoading);
            // 
            // hy_group其它
            // 
            this.hy_group其它.Items.Add(this.checkBox叠加显示辅助);
            this.hy_group其它.Items.Add(this.checkBox监控剪贴板);
            this.hy_group其它.Items.Add(this.menuHY);
            this.hy_group其它.Items.Add(this.separator6);
            this.hy_group其它.Items.Add(this.menu修复);
            this.hy_group其它.Items.Add(this.hy_menu设置);
            this.hy_group其它.Items.Add(this.buttonAboutHy);
            this.hy_group其它.Label = "其它";
            this.hy_group其它.Name = "hy_group其它";
            this.hy_group其它.Visible = false;
            // 
            // checkBox叠加显示辅助
            // 
            this.checkBox叠加显示辅助.Label = "叠加显示辅助";
            this.checkBox叠加显示辅助.Name = "checkBox叠加显示辅助";
            this.checkBox叠加显示辅助.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.checkBoxHy叠加显示辅助_Click);
            // 
            // checkBox监控剪贴板
            // 
            this.checkBox监控剪贴板.Label = "监控剪贴板";
            this.checkBox监控剪贴板.Name = "checkBox监控剪贴板";
            this.checkBox监控剪贴板.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.checkBoxHy监控剪贴板_Click);
            // 
            // menuHY
            // 
            this.menuHY.Items.Add(this.checkBoxStockHelper);
            this.menuHY.Items.Add(this.button考勤);
            this.menuHY.Label = "其它";
            this.menuHY.Name = "menuHY";
            this.menuHY.OfficeImageId = "ActiveXComboBox";
            this.menuHY.ShowImage = true;
            this.menuHY.Visible = false;
            // 
            // checkBoxStockHelper
            // 
            this.checkBoxStockHelper.Checked = true;
            this.checkBoxStockHelper.Label = "StockHelper";
            this.checkBoxStockHelper.Name = "checkBoxStockHelper";
            this.checkBoxStockHelper.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.checkBoxStockHelper_Click);
            // 
            // button考勤
            // 
            this.button考勤.Label = "考勤";
            this.button考勤.Name = "button考勤";
            this.button考勤.OfficeImageId = "Calendar";
            this.button考勤.ShowImage = true;
            this.button考勤.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.button考勤_Click);
            // 
            // separator6
            // 
            this.separator6.Name = "separator6";
            // 
            // menu修复
            // 
            this.menu修复.Items.Add(this.button6);
            this.menu修复.Items.Add(this.button重置单元格备注大小);
            this.menu修复.Label = "修复";
            this.menu修复.Name = "menu修复";
            this.menu修复.OfficeImageId = "AddInManager";
            this.menu修复.ShowImage = true;
            // 
            // button6
            // 
            this.button6.Label = "Excel修复";
            this.button6.Name = "button6";
            this.button6.OfficeImageId = "Refresh";
            this.button6.ShowImage = true;
            this.button6.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHyExcel修复_Click);
            // 
            // button重置单元格备注大小
            // 
            this.button重置单元格备注大小.Label = "重置单元格备注大小";
            this.button重置单元格备注大小.Name = "button重置单元格备注大小";
            this.button重置单元格备注大小.OfficeImageId = "Comment";
            this.button重置单元格备注大小.ShowImage = true;
            this.button重置单元格备注大小.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy重置单元格备注大小_Click);
            // 
            // hy_menu设置
            // 
            this.hy_menu设置.Items.Add(this.chk显示0值);
            this.hy_menu设置.Items.Add(this.checkBox分级标记);
            this.hy_menu设置.Items.Add(this.separator1);
            this.hy_menu设置.Items.Add(this.buttonini配置文件);
            this.hy_menu设置.Items.Add(this.button配置目录);
            this.hy_menu设置.Items.Add(this.separator5);
            this.hy_menu设置.Label = "设置";
            this.hy_menu设置.Name = "hy_menu设置";
            this.hy_menu设置.OfficeImageId = "ComAddInsDialog";
            this.hy_menu设置.ShowImage = true;
            // 
            // chk显示0值
            // 
            this.chk显示0值.Checked = true;
            this.chk显示0值.Label = "显示0值";
            this.chk显示0值.Name = "chk显示0值";
            this.chk显示0值.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.checkBoxHy显示0值_Click);
            // 
            // checkBox分级标记
            // 
            this.checkBox分级标记.Checked = true;
            this.checkBox分级标记.Label = "分级标记";
            this.checkBox分级标记.Name = "checkBox分级标记";
            this.checkBox分级标记.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.checkBoxHy分级标记_Click);
            // 
            // separator1
            // 
            this.separator1.Name = "separator1";
            // 
            // buttonini配置文件
            // 
            this.buttonini配置文件.Label = "ini配置文件";
            this.buttonini配置文件.Name = "buttonini配置文件";
            this.buttonini配置文件.OfficeImageId = "Properties";
            this.buttonini配置文件.ShowImage = true;
            this.buttonini配置文件.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHyini配置文件_Click);
            // 
            // button配置目录
            // 
            this.button配置目录.Label = "打开配置目录";
            this.button配置目录.Name = "button配置目录";
            this.button配置目录.OfficeImageId = "Folder";
            this.button配置目录.ShowImage = true;
            this.button配置目录.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy配置目录_Click);
            // 
            // separator5
            // 
            this.separator5.Name = "separator5";
            // 
            // buttonAboutHy
            // 
            this.buttonAboutHy.Label = "关于";
            this.buttonAboutHy.Name = "buttonAboutHy";
            this.buttonAboutHy.OfficeImageId = "Help";
            this.buttonAboutHy.ShowImage = true;
            this.buttonAboutHy.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHyAbout_Click);
            // 
            // group1
            // 
            this.group1.Items.Add(this.button2);
            this.group1.Items.Add(this.button3);
            this.group1.Label = "关于";
            this.group1.Name = "group1";
            // 
            // button2
            // 
            this.button2.Label = "Excel修复";
            this.button2.Name = "button2";
            this.button2.OfficeImageId = "Refresh";
            this.button2.ShowImage = true;
            // 
            // button3
            // 
            this.button3.Label = "关于";
            this.button3.Name = "button3";
            this.button3.OfficeImageId = "Help";
            this.button3.ShowImage = true;
            // 
            // znTab
            // 
            this.znTab.Groups.Add(this.zn_group字符格式);
            this.znTab.Groups.Add(this.zn_groupOffice);
            this.znTab.Groups.Add(this.zn_group文件);
            this.znTab.Groups.Add(this.zn_group无线);
            this.znTab.Groups.Add(this.zn_group其它);
            this.znTab.Label = "ZnTools";
            this.znTab.Name = "znTab";
            // 
            // zn_group字符格式
            // 
            this.zn_group字符格式.Items.Add(this.btn标记提取规整字符串a);
            this.zn_group字符格式.Items.Add(this.btn填写合规性检查abc);
            this.zn_group字符格式.Items.Add(this.button17);
            this.zn_group字符格式.Items.Add(this.separator9);
            this.zn_group字符格式.Items.Add(this.button11);
            this.zn_group字符格式.Items.Add(this.button12);
            this.zn_group字符格式.Items.Add(this.button13);
            this.zn_group字符格式.Label = "字符/格式";
            this.zn_group字符格式.Name = "zn_group字符格式";
            // 
            // btn标记提取规整字符串a
            // 
            this.btn标记提取规整字符串a.Label = "标记/提取/规整字符";
            this.btn标记提取规整字符串a.Name = "btn标记提取规整字符串a";
            this.btn标记提取规整字符串a.OfficeImageId = "DropCapOptionsDialog";
            this.btn标记提取规整字符串a.ShowImage = true;
            this.btn标记提取规整字符串a.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy字符串处理_Click);
            // 
            // btn填写合规性检查abc
            // 
            this.btn填写合规性检查abc.Label = "填写合规性检查";
            this.btn填写合规性检查abc.Name = "btn填写合规性检查abc";
            this.btn填写合规性检查abc.OfficeImageId = "SlideMasterTextPlaceholderInsert";
            this.btn填写合规性检查abc.ShowImage = true;
            this.btn填写合规性检查abc.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy填写合规检查_Click);
            // 
            // button17
            // 
            this.button17.Label = "向下填充";
            this.button17.Name = "button17";
            this.button17.OfficeImageId = "ConvertTextToTable";
            this.button17.ShowImage = true;
            // 
            // separator9
            // 
            this.separator9.Name = "separator9";
            // 
            // button11
            // 
            this.button11.Label = "删除外部链接";
            this.button11.Name = "button11";
            this.button11.OfficeImageId = "HyperlinkInsert";
            this.button11.ShowImage = true;
            this.button11.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy删除外部链接_Click);
            // 
            // button12
            // 
            this.button12.Label = "设置页眉脚";
            this.button12.Name = "button12";
            this.button12.OfficeImageId = "PageSetup";
            this.button12.ShowImage = true;
            this.button12.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy设置页眉脚_Click);
            // 
            // button13
            // 
            this.button13.Label = "设置倍数行高";
            this.button13.Name = "button13";
            this.button13.OfficeImageId = "TableShowGridlines";
            this.button13.ShowImage = true;
            this.button13.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy设置倍数行高_Click);
            // 
            // zn_groupOffice
            // 
            this.zn_groupOffice.Items.Add(this.buttonPPT生成修改转PDF_B);
            this.zn_groupOffice.Items.Add(this.buttonWord生成修改转PDF_B);
            this.zn_groupOffice.Label = "Office";
            this.zn_groupOffice.Name = "zn_groupOffice";
            // 
            // buttonPPT生成修改转PDF_B
            // 
            this.buttonPPT生成修改转PDF_B.Label = "PPT批量生成/修改/转PDF";
            this.buttonPPT生成修改转PDF_B.Name = "buttonPPT生成修改转PDF_B";
            this.buttonPPT生成修改转PDF_B.OfficeImageId = "FileSaveAsPowerPoint97_2003";
            this.buttonPPT生成修改转PDF_B.ShowImage = true;
            this.buttonPPT生成修改转PDF_B.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHyPPT助手_Click);
            // 
            // buttonWord生成修改转PDF_B
            // 
            this.buttonWord生成修改转PDF_B.Label = "Word批量生成/修改/转PDF";
            this.buttonWord生成修改转PDF_B.Name = "buttonWord生成修改转PDF_B";
            this.buttonWord生成修改转PDF_B.OfficeImageId = "FileSaveAsWord97_2003";
            this.buttonWord生成修改转PDF_B.ShowImage = true;
            this.buttonWord生成修改转PDF_B.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHyWord助手_Click);
            // 
            // zn_group文件
            // 
            this.zn_group文件.Items.Add(this.button14);
            this.zn_group文件.Items.Add(this.button批量找文件);
            this.zn_group文件.Items.Add(this.separator11);
            this.zn_group文件.Items.Add(this.button9);
            this.zn_group文件.Items.Add(this.button7);
            this.zn_group文件.Label = "文件";
            this.zn_group文件.Name = "zn_group文件";
            // 
            // button14
            // 
            this.button14.Label = "临时/发送/存档";
            this.button14.Name = "button14";
            this.button14.OfficeImageId = "FileSaveAs";
            this.button14.ShowImage = true;
            this.button14.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy发送及存档_Click);
            // 
            // button批量找文件
            // 
            this.button批量找文件.Label = "文件查找/复制/改名";
            this.button批量找文件.Name = "button批量找文件";
            this.button批量找文件.OfficeImageId = "SourceControlShowDifferences";
            this.button批量找文件.ShowImage = true;
            this.button批量找文件.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZn文件助手_Click);
            // 
            // separator11
            // 
            this.separator11.Name = "separator11";
            // 
            // button9
            // 
            this.button9.Label = "最近打开文件";
            this.button9.Name = "button9";
            this.button9.OfficeImageId = "FileOpen";
            this.button9.ShowImage = true;
            this.button9.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy最近打开文件_Click);
            // 
            // button7
            // 
            this.button7.Label = "Wps/Excel切换";
            this.button7.Name = "button7";
            this.button7.OfficeImageId = "WindowSwitchWindowsMenuExcel";
            this.button7.ShowImage = true;
            // 
            // zn_group无线
            // 
            this.zn_group无线.Items.Add(this.button16);
            this.zn_group无线.Items.Add(this.button23);
            this.zn_group无线.Items.Add(this.button24);
            this.zn_group无线.Items.Add(this.button15);
            this.zn_group无线.Items.Add(this.button51ToolsV1);
            this.zn_group无线.Label = "无线";
            this.zn_group无线.Name = "zn_group无线";
            this.zn_group无线.Visible = false;
            // 
            // button16
            // 
            this.button16.Label = "批量查找站点";
            this.button16.Name = "button16";
            this.button16.OfficeImageId = "FileWorkflowTasks";
            this.button16.ShowImage = true;
            this.button16.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZn批量查找站点_Click);
            // 
            // button23
            // 
            this.button23.Label = "生成地理图层";
            this.button23.Name = "button23";
            this.button23.OfficeImageId = "PictureReflectionGallery";
            this.button23.ShowImage = true;
            this.button23.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy生成地理图层_Click);
            // 
            // button24
            // 
            this.button24.Label = "格式化经纬度";
            this.button24.Name = "button24";
            this.button24.OfficeImageId = "WordCount";
            this.button24.ShowImage = true;
            this.button24.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy格式化经纬度_Click);
            // 
            // button15
            // 
            this.button15.Label = "订单文件生成kml图层";
            this.button15.Name = "button15";
            this.button15.OfficeImageId = "ClipArtInsert";
            this.button15.ShowImage = true;
            // 
            // button51ToolsV1
            // 
            this.button51ToolsV1.Label = "51助手";
            this.button51ToolsV1.Name = "button51ToolsV1";
            this.button51ToolsV1.OfficeImageId = "PivotTableLayoutShowInOutlineForm";
            this.button51ToolsV1.ShowImage = true;
            this.button51ToolsV1.Visible = false;
            this.button51ToolsV1.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy51小工具V1_Click);
            // 
            // zn_group其它
            // 
            this.zn_group其它.Items.Add(this.checkBoxVerticalHighlight);
            this.zn_group其它.Items.Add(this.checkBoxHorizontalHighlight);
            this.zn_group其它.Items.Add(this.separator10);
            this.zn_group其它.Items.Add(this.menu3);
            this.zn_group其它.Items.Add(this.menu5);
            this.zn_group其它.Items.Add(this.buttonAboutZn);
            this.zn_group其它.Label = "其它";
            this.zn_group其它.Name = "zn_group其它";
            // 
            // checkBoxVerticalHighlight
            // 
            this.checkBoxVerticalHighlight.Checked = true;
            this.checkBoxVerticalHighlight.Label = "垂直高亮行列";
            this.checkBoxVerticalHighlight.Name = "checkBoxVerticalHighlight";
            this.checkBoxVerticalHighlight.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.checkBoxZn垂直高亮行列_Click);
            // 
            // checkBoxHorizontalHighlight
            // 
            this.checkBoxHorizontalHighlight.Checked = true;
            this.checkBoxHorizontalHighlight.Label = "水平高亮行列";
            this.checkBoxHorizontalHighlight.Name = "checkBoxHorizontalHighlight";
            this.checkBoxHorizontalHighlight.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.checkBoxZn水平高亮行列_Click);
            // 
            // separator10
            // 
            this.separator10.Name = "separator10";
            // 
            // menu3
            // 
            this.menu3.Items.Add(this.button1);
            this.menu3.Items.Add(this.button4);
            this.menu3.Label = "设置";
            this.menu3.Name = "menu3";
            this.menu3.OfficeImageId = "AddInManager";
            this.menu3.ShowImage = true;
            // 
            // button1
            // 
            this.button1.Label = "ini配置文件";
            this.button1.Name = "button1";
            this.button1.OfficeImageId = "Properties";
            this.button1.ShowImage = true;
            this.button1.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHyini配置文件_Click);
            // 
            // button4
            // 
            this.button4.Label = "打开配置目录";
            this.button4.Name = "button4";
            this.button4.OfficeImageId = "Folder";
            this.button4.ShowImage = true;
            this.button4.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy配置目录_Click);
            // 
            // menu5
            // 
            this.menu5.Items.Add(this.button20);
            this.menu5.Items.Add(this.button26);
            this.menu5.Label = "修复";
            this.menu5.Name = "menu5";
            this.menu5.OfficeImageId = "ComAddInsDialog";
            this.menu5.ShowImage = true;
            // 
            // button20
            // 
            this.button20.Label = "Excel修复";
            this.button20.Name = "button20";
            this.button20.OfficeImageId = "Refresh";
            this.button20.ShowImage = true;
            this.button20.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHyExcel修复_Click);
            // 
            // button26
            // 
            this.button26.Label = "重置单元格备注大小";
            this.button26.Name = "button26";
            this.button26.OfficeImageId = "Comment";
            this.button26.ShowImage = true;
            this.button26.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy重置单元格备注大小_Click);
            // 
            // buttonAboutZn
            // 
            this.buttonAboutZn.Label = "关于";
            this.buttonAboutZn.Name = "buttonAboutZn";
            this.buttonAboutZn.OfficeImageId = "Help";
            this.buttonAboutZn.ShowImage = true;
            this.buttonAboutZn.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHyAbout_Click);
            // 
            // znAbout
            // 
            this.znAbout.Groups.Add(this.znAboutGroup);
            this.znAbout.Label = "ZnAbout";
            this.znAbout.Name = "znAbout";
            // 
            // znAboutGroup
            // 
            this.znAboutGroup.Items.Add(this.znAboutButton);
            this.znAboutGroup.Label = "授权";
            this.znAboutGroup.Name = "znAboutGroup";
            // 
            // znAboutButton
            // 
            this.znAboutButton.Label = "授权";
            this.znAboutButton.Name = "znAboutButton";
            this.znAboutButton.OfficeImageId = "Lock";
            this.znAboutButton.ShowImage = true;
            this.znAboutButton.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHyAbout_Click);
            // 
            // menu设置其它
            // 
            this.menu设置其它.Items.Add(this.btn设置倍数行高);
            this.menu设置其它.Items.Add(this.separator4);
            this.menu设置其它.Items.Add(this.btn金额转大写);
            this.menu设置其它.Items.Add(this.btn隐藏范围外内容);
            this.menu设置其它.Label = "其它";
            this.menu设置其它.Name = "menu设置其它";
            this.menu设置其它.OfficeImageId = "Bullets";
            this.menu设置其它.ShowImage = true;
            // 
            // btn设置倍数行高
            // 
            this.btn设置倍数行高.Label = "设置倍数行高";
            this.btn设置倍数行高.Name = "btn设置倍数行高";
            this.btn设置倍数行高.OfficeImageId = "SizeToFit";
            this.btn设置倍数行高.ShowImage = true;
            this.btn设置倍数行高.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonZnHy设置倍数行高_Click);
            // 
            // separator4
            // 
            this.separator4.Name = "separator4";
            // 
            // btn金额转大写
            // 
            this.btn金额转大写.Label = "金额转大写";
            this.btn金额转大写.Name = "btn金额转大写";
            this.btn金额转大写.OfficeImageId = "Font";
            this.btn金额转大写.ShowImage = true;
            this.btn金额转大写.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHy金额转大写_Click);
            // 
            // btn隐藏范围外内容
            // 
            this.btn隐藏范围外内容.Label = "隐藏选区外";
            this.btn隐藏范围外内容.Name = "btn隐藏范围外内容";
            this.btn隐藏范围外内容.OfficeImageId = "WindowHide";
            this.btn隐藏范围外内容.ShowImage = true;
            this.btn隐藏范围外内容.Click += new Microsoft.Office.Tools.Ribbon.RibbonControlEventHandler(this.buttonHy隐藏范围外内容_Click);
            // 
            // HyRibbonClass
            // 
            this.Name = "HyRibbonClass";
            this.RibbonType = "Microsoft.Excel.Workbook";
            this.Tabs.Add(this.znTab);
            this.Tabs.Add(this.hyTab);
            this.Tabs.Add(this.znAbout);
            this.Load += new Microsoft.Office.Tools.Ribbon.RibbonUIEventHandler(this.hyRibbon1_Load);
            this.hyTab.ResumeLayout(false);
            this.hyTab.PerformLayout();
            this.group字符格式.ResumeLayout(false);
            this.group字符格式.PerformLayout();
            this.group标记标签.ResumeLayout(false);
            this.group标记标签.PerformLayout();
            this.group数据处理.ResumeLayout(false);
            this.group数据处理.PerformLayout();
            this.groupOffice.ResumeLayout(false);
            this.groupOffice.PerformLayout();
            this.group文件.ResumeLayout(false);
            this.group文件.PerformLayout();
            this.group无线.ResumeLayout(false);
            this.group无线.PerformLayout();
            this.group2.ResumeLayout(false);
            this.group2.PerformLayout();
            this.hy_group其它.ResumeLayout(false);
            this.hy_group其它.PerformLayout();
            this.group1.ResumeLayout(false);
            this.group1.PerformLayout();
            this.znTab.ResumeLayout(false);
            this.znTab.PerformLayout();
            this.zn_group字符格式.ResumeLayout(false);
            this.zn_group字符格式.PerformLayout();
            this.zn_groupOffice.ResumeLayout(false);
            this.zn_groupOffice.PerformLayout();
            this.zn_group文件.ResumeLayout(false);
            this.zn_group文件.PerformLayout();
            this.zn_group无线.ResumeLayout(false);
            this.zn_group无线.PerformLayout();
            this.zn_group其它.ResumeLayout(false);
            this.zn_group其它.PerformLayout();
            this.znAbout.ResumeLayout(false);
            this.znAbout.PerformLayout();
            this.znAboutGroup.ResumeLayout(false);
            this.znAboutGroup.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        internal Microsoft.Office.Tools.Ribbon.RibbonTab hyTab;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn批量查找;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn标记提取规整字符串b;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn设置倍数行高;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn隐藏范围外内容;
        internal Microsoft.Office.Tools.Ribbon.RibbonCheckBox chk显示0值;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn发送及存档;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn设置页眉脚;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn填写合规检查;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn金额转大写;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn自动脚本;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn格式化经纬度;
        internal Microsoft.Office.Tools.Ribbon.RibbonMenu menu其它3;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group字符格式;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group无线;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup hy_group其它;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button标签填写筛选;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button文件操作;
        internal Microsoft.Office.Tools.Ribbon.RibbonMenu menu设置其它;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button清除所选条件格式;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button清除全表条件格式;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonAboutHy;
        internal Microsoft.Office.Tools.Ribbon.RibbonCheckBox checkBox监控剪贴板;
        internal Microsoft.Office.Tools.Ribbon.RibbonGallery gallery脚本内容;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button打开脚本表;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button取消条件格式并取消筛选;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button重置单元格备注大小;
        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator7;
        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator8;
        internal Microsoft.Office.Tools.Ribbon.RibbonMenu menu1;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button生成地理图层;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button通过GPS计算最近站点;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button向下填充;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonWpsExcel切换;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button专用工具;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btm工作表管理;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonWordHelper;
        internal Microsoft.Office.Tools.Ribbon.RibbonCheckBox checkBox叠加显示辅助;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button外部链接;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonVisioHelper;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup groupOffice;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group1;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button2;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button3;
        internal Microsoft.Office.Tools.Ribbon.RibbonGallery gallery常用文件;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button复制当前文件路径;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button同步数据;
        internal Microsoft.Office.Tools.Ribbon.RibbonMenu hy_menu设置;
        internal Microsoft.Office.Tools.Ribbon.RibbonCheckBox checkBox分级标记;
        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator1;
        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator4;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonAI辅助填写;
        internal Microsoft.Office.Tools.Ribbon.RibbonCheckBox checkBoxStockHelper;
        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator5;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonini配置文件;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button考勤;
        public Microsoft.Office.Tools.Ribbon.RibbonButton buttonPPTHelper;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group数据处理;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group标记标签;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group文件;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button配置目录;
        internal Microsoft.Office.Tools.Ribbon.RibbonMenu menuHY;
        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator3;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button51ToolsV2b;
        internal Microsoft.Office.Tools.Ribbon.RibbonMenu menu修复;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button6;
        internal Microsoft.Office.Tools.Ribbon.RibbonTab znTab;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup zn_group字符格式;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn标记提取规整字符串a;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton btn填写合规性检查abc;

        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator10;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button11;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button12;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button13;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup zn_groupOffice;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonPPT生成修改转PDF_B;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonWord生成修改转PDF_B;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup zn_group文件;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button批量找文件;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button16;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup zn_group其它;
        internal Microsoft.Office.Tools.Ribbon.RibbonMenu menu3;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button1;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button4;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup zn_group无线;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button23;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button24;
        internal Microsoft.Office.Tools.Ribbon.RibbonMenu menu5;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button20;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button26;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button14;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonAboutZn;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup group2;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button51ToolsV1;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button51ToolsV1b;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button5;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button记录当前文件;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button9;
        internal Microsoft.Office.Tools.Ribbon.RibbonCheckBox checkBoxVerticalHighlight;
        internal Microsoft.Office.Tools.Ribbon.RibbonCheckBox checkBoxHorizontalHighlight;
        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator6;
        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator9;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton buttonDevelopTest;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button8;
        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator2;
        internal Microsoft.Office.Tools.Ribbon.RibbonGroup znAboutGroup;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton znAboutButton;
        public Microsoft.Office.Tools.Ribbon.RibbonTab znAbout;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button17;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button7;
        internal Microsoft.Office.Tools.Ribbon.RibbonButton button15;
        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator11;
        internal Microsoft.Office.Tools.Ribbon.RibbonSeparator separator12;
    }

    partial class ThisRibbonCollection
    {
        internal HyRibbonClass hyRibbon
        {
            get { return this.GetRibbon<HyRibbonClass>(); }
        }
    }
}
