# TextBox历史记录功能使用说明

## 概述

TextBox历史记录功能是参照现有的`BindComboBox`功能实现的，为TextBox控件提供了完整的历史记录管理功能。通过右键菜单的方式，用户可以方便地保存、查看和使用历史输入记录。

## 核心功能

### 1. 右键菜单功能
**文本操作功能组（顶部）：**
- **复制**：复制用户选定的文本到剪贴板
- **复制所有**：复制文本框所有内容到剪贴板
- **清空**：清空文本框内容

**历史记录功能组（底部）：**
- **保存**：将当前文本框内容保存到历史记录
- **历史**：显示历史记录列表，点击可快速填入
  - 历史记录项：点击可快速填入对应内容
  - **清除历史**：清除所有历史记录（位于历史子菜单底部）

### 2. 数据存储特性
- **存储位置**：`Data\ET\Cache\ETForm\TextBoxHistory\` 目录
- **存储格式**：JSON格式，便于阅读和维护
- **文件命名**：`{窗体类名}_{控件名}.json`
- **历史数量**：可配置最大历史记录数量（默认10条）
- **自动排序**：最新记录自动排在最前面

### 3. JSON数据结构
```json
{
  "History": [
    "最新的输入内容",
    "第二新的输入内容",
    "更早的输入内容"
  ],
  "MaxHistoryCount": 10,
  "CreatedTime": "2025-07-30T10:30:45.123",
  "LastUpdatedTime": "2025-07-30T11:15:20.456"
}
```

## 使用方法

### 1. 基本使用（推荐）
```csharp
using ET;

// 在窗体Load事件或初始化方法中调用
private void Form_Load(object sender, EventArgs e)
{
    // 使用默认设置：最多10条记录，自动填充最新值
    ETForm.BindTextBox(textBox1);
}
```

### 2. 自定义配置
```csharp
// 自定义历史记录数量和自动填充设置
ETForm.BindTextBox(textBox1, maxHistoryCount: 15, autoFillLatestValue: false);
```

### 3. 使用自定义键名
```csharp
// 使用自定义historyKey，适用于需要特殊命名的场景
ETForm.BindTextBox(textBox1, "CustomKey_UserInput", 20, true);
```

## 方法参数说明

### BindTextBox 方法重载

#### 重载1：自动生成键名（推荐）
```csharp
public static void BindTextBox(TextBox textBox, int maxHistoryCount = 10, bool autoFillLatestValue = true)
```

**参数说明：**
- `textBox`：要绑定的TextBox控件
- `maxHistoryCount`：最大历史记录数量，默认10条
- `autoFillLatestValue`：是否自动填充最近的值，默认true

#### 重载2：自定义键名
```csharp
public static void BindTextBox(TextBox textBox, string historyKey, int maxHistoryCount = 10, bool autoFillLatestValue = true)
```

**参数说明：**
- `textBox`：要绑定的TextBox控件
- `historyKey`：历史记录的唯一标识键，用于生成文件名
- `maxHistoryCount`：最大历史记录数量，默认10条
- `autoFillLatestValue`：是否自动填充最近的值，默认true

## 实际应用示例

### 示例1：用户输入框
```csharp
public partial class UserInputForm : Form
{
    private void UserInputForm_Load(object sender, EventArgs e)
    {
        // 为用户名输入框绑定历史记录
        ETForm.BindTextBox(textBoxUserName);
        
        // 为搜索框绑定历史记录，保存更多历史
        ETForm.BindTextBox(textBoxSearch, maxHistoryCount: 20);
        
        // 为临时输入框绑定历史记录，不自动填充
        ETForm.BindTextBox(textBoxTemp, maxHistoryCount: 5, autoFillLatestValue: false);
    }
}
```

### 示例2：配置文件路径输入
```csharp
public partial class ConfigForm : Form
{
    private void ConfigForm_Load(object sender, EventArgs e)
    {
        // 为文件路径输入框绑定历史记录
        ETForm.BindTextBox(textBoxFilePath, "ConfigForm_FilePath", 15);
        
        // 为输出目录输入框绑定历史记录
        ETForm.BindTextBox(textBoxOutputDir, "ConfigForm_OutputDir", 15);
    }
}
```

## 技术特性

### 1. 安全性
- 完善的异常处理机制
- 文件操作安全保护
- 空值和无效输入检查

### 2. 性能优化
- 延迟加载历史记录菜单
- 最小化文件I/O操作
- 内存使用优化

### 3. 用户体验
- 直观的右键菜单操作
- 即时的操作反馈
- 确认对话框防止误操作

### 4. 兼容性
- 与现有BindComboBox功能保持一致的设计风格
- 使用相同的目录结构和命名规范
- 完全兼容现有的ETForm工具类

## 注意事项

1. **控件命名**：建议为TextBox控件设置有意义的Name属性，以便生成清晰的历史记录文件名
2. **历史记录数量**：根据实际需要设置合适的历史记录数量，避免文件过大
3. **自动填充**：对于敏感信息输入框，建议设置`autoFillLatestValue = false`
4. **文件权限**：确保应用程序对`Data\ET\Cache\ETForm\TextBoxHistory\`目录有读写权限

## 错误处理

所有操作都包含完善的异常处理机制，错误信息会通过ETLogManager记录到日志中，不会影响应用程序的正常运行。

## 与BindComboBox的对比

| 特性 | BindComboBox | BindTextBox |
|------|--------------|-------------|
| 存储格式 | 文本文件(.data) | JSON文件(.json) |
| 操作方式 | 下拉选择 | 右键菜单 |
| 功能丰富度 | 基础历史记录 | 历史+复制+清空 |
| 配置灵活性 | 标准配置 | 增强配置 |
| 用户交互 | 自动化 | 手动触发 |

## 更新日志

- **v1.0**：初始版本，实现基本的历史记录功能和右键菜单
