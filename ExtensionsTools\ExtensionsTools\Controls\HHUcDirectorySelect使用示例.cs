using System;
using System.Windows.Forms;
using ET.Controls;

namespace ET.Controls.Examples
{
    /// <summary>
    /// HHUcDirectorySelect 控件使用示例
    /// </summary>
    /// <remarks>
    /// 此示例展示了如何使用带历史记录功能的目录选择控件：
    /// 1. 基本的目录选择功能
    /// 2. 历史记录自动管理
    /// 3. 事件处理和响应
    /// 4. 属性配置和自定义
    /// 
    /// 新增历史记录功能特性：
    /// - 自动保存最近30个使用的目录路径
    /// - 下拉列表显示历史记录
    /// - 支持自动填充最近使用的路径
    /// - 历史记录按窗体和控件名称独立存储
    /// - 重复路径自动去重并移至最前
    /// </remarks>
    public partial class HHUcDirectorySelectExample : Form
    {
        private HHUcDirectorySelect directorySelect1;
        private HHUcDirectorySelect directorySelect2;
        private Label lblSelectedPath;
        private Button btnGetPath;
        private Button btnSaveHistory;
        private CheckBox chkAutoFill;

        public HHUcDirectorySelectExample()
        {
            InitializeComponent();
            SetupEventHandlers();
        }

        private void InitializeComponent()
        {
            this.directorySelect1 = new HHUcDirectorySelect();
            this.directorySelect2 = new HHUcDirectorySelect();
            this.lblSelectedPath = new Label();
            this.btnGetPath = new Button();
            this.btnSaveHistory = new Button();
            this.chkAutoFill = new CheckBox();
            this.SuspendLayout();

            // 
            // directorySelect1
            // 
            this.directorySelect1.Location = new System.Drawing.Point(12, 12);
            this.directorySelect1.Name = "directorySelect1";
            this.directorySelect1.Size = new System.Drawing.Size(400, 21);
            this.directorySelect1.TabIndex = 0;
            this.directorySelect1.AutoFillLatestValue = true; // 启用自动填充最近值

            // 
            // directorySelect2
            // 
            this.directorySelect2.Location = new System.Drawing.Point(12, 50);
            this.directorySelect2.Name = "directorySelect2";
            this.directorySelect2.Size = new System.Drawing.Size(400, 21);
            this.directorySelect2.TabIndex = 1;
            this.directorySelect2.AutoFillLatestValue = false; // 禁用自动填充

            // 
            // lblSelectedPath
            // 
            this.lblSelectedPath.Location = new System.Drawing.Point(12, 90);
            this.lblSelectedPath.Name = "lblSelectedPath";
            this.lblSelectedPath.Size = new System.Drawing.Size(400, 40);
            this.lblSelectedPath.TabIndex = 2;
            this.lblSelectedPath.Text = "选择的路径将显示在这里";

            // 
            // btnGetPath
            // 
            this.btnGetPath.Location = new System.Drawing.Point(12, 140);
            this.btnGetPath.Name = "btnGetPath";
            this.btnGetPath.Size = new System.Drawing.Size(100, 23);
            this.btnGetPath.TabIndex = 3;
            this.btnGetPath.Text = "获取路径";
            this.btnGetPath.UseVisualStyleBackColor = true;

            // 
            // btnSaveHistory
            // 
            this.btnSaveHistory.Location = new System.Drawing.Point(130, 140);
            this.btnSaveHistory.Name = "btnSaveHistory";
            this.btnSaveHistory.Size = new System.Drawing.Size(100, 23);
            this.btnSaveHistory.TabIndex = 4;
            this.btnSaveHistory.Text = "保存历史记录";
            this.btnSaveHistory.UseVisualStyleBackColor = true;

            // 
            // chkAutoFill
            // 
            this.chkAutoFill.AutoSize = true;
            this.chkAutoFill.Checked = true;
            this.chkAutoFill.CheckState = CheckState.Checked;
            this.chkAutoFill.Location = new System.Drawing.Point(250, 144);
            this.chkAutoFill.Name = "chkAutoFill";
            this.chkAutoFill.Size = new System.Drawing.Size(120, 16);
            this.chkAutoFill.TabIndex = 5;
            this.chkAutoFill.Text = "自动填充最近路径";
            this.chkAutoFill.UseVisualStyleBackColor = true;

            // 
            // HHUcDirectorySelectExample
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(450, 200);
            this.Controls.Add(this.chkAutoFill);
            this.Controls.Add(this.btnSaveHistory);
            this.Controls.Add(this.btnGetPath);
            this.Controls.Add(this.lblSelectedPath);
            this.Controls.Add(this.directorySelect2);
            this.Controls.Add(this.directorySelect1);
            this.Name = "HHUcDirectorySelectExample";
            this.Text = "目录选择控件使用示例（带历史记录）";
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 路径选择事件
            directorySelect1.OnPathSelected += DirectorySelect1_OnPathSelected;
            directorySelect2.OnPathSelected += DirectorySelect2_OnPathSelected;

            // 文本变更事件
            directorySelect1.TextChanged += DirectorySelect1_TextChanged;

            // 按钮点击事件
            btnGetPath.Click += BtnGetPath_Click;
            btnSaveHistory.Click += BtnSaveHistory_Click;
            chkAutoFill.CheckedChanged += ChkAutoFill_CheckedChanged;
        }

        /// <summary>
        /// 目录选择1路径选择事件处理
        /// </summary>
        private void DirectorySelect1_OnPathSelected(string filePath)
        {
            lblSelectedPath.Text = $"控件1选择的路径: {filePath}";
            // 历史记录会自动保存，无需手动处理
        }

        /// <summary>
        /// 目录选择2路径选择事件处理
        /// </summary>
        private void DirectorySelect2_OnPathSelected(string filePath)
        {
            lblSelectedPath.Text = $"控件2选择的路径: {filePath}";
        }

        /// <summary>
        /// 文本变更事件处理
        /// </summary>
        private void DirectorySelect1_TextChanged(object sender, EventArgs e)
        {
            // 可以在这里处理文本变更逻辑
            // 注意：历史记录只在通过对话框选择时自动保存
        }

        /// <summary>
        /// 获取路径按钮点击事件
        /// </summary>
        private void BtnGetPath_Click(object sender, EventArgs e)
        {
            string path1 = directorySelect1.Text;
            string path2 = directorySelect2.Text;
            
            lblSelectedPath.Text = $"控件1: {path1}\n控件2: {path2}";
        }

        /// <summary>
        /// 保存历史记录按钮点击事件
        /// </summary>
        private void BtnSaveHistory_Click(object sender, EventArgs e)
        {
            // 手动保存当前路径到历史记录
            directorySelect1.SavePathHistoryToFile();
            directorySelect2.SavePathHistoryToFile();
            
            MessageBox.Show("历史记录已保存！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// 自动填充选项变更事件
        /// </summary>
        private void ChkAutoFill_CheckedChanged(object sender, EventArgs e)
        {
            directorySelect1.AutoFillLatestValue = chkAutoFill.Checked;
            directorySelect2.AutoFillLatestValue = chkAutoFill.Checked;
        }
    }

    /// <summary>
    /// 程序入口点示例
    /// </summary>
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new HHUcDirectorySelectExample());
        }
    }
}
